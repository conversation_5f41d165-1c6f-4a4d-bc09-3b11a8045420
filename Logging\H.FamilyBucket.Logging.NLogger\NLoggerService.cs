﻿using System;
using System.IO;
using NLog;
using NLog.Config;

namespace H.FamilyBucket.Logging.NLogger
{
    /// <summary>
    /// NLog日志服务实现
    /// </summary>
    public class NLoggerService : ILoggerService
    {
        private readonly Logger _logger;
        private static readonly object _lock = new object();
        private static bool _isConfigured = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="categoryName">日志类别名称</param>
        /// <param name="configuration">日志配置</param>
        public NLoggerService(string? categoryName = null, LoggerConfiguration? configuration = null)
        {
            InitializeNLog(configuration);
            _logger = LogManager.GetLogger(categoryName ?? "Default");
        }

        /// <summary>
        /// 初始化NLog配置
        /// </summary>
        /// <param name="configuration">日志配置</param>
        private static void InitializeNLog(LoggerConfiguration? configuration = null)
        {
            if (_isConfigured) return;

            lock (_lock)
            {
                if (_isConfigured) return;

                configuration = configuration ?? new LoggerConfiguration();

                // 如果配置文件存在，则使用配置文件
                if (File.Exists(configuration.ConfigFilePath))
                {
                    LogManager.Configuration = new XmlLoggingConfiguration(configuration.ConfigFilePath);
                }
                else
                {
                    // 如果配置文件不存在，则使用代码配置
                    CreateProgrammaticConfiguration(configuration);
                }

                _isConfigured = true;
            }
        }

        /// <summary>
        /// 创建程序化配置
        /// </summary>
        /// <param name="config">日志配置</param>
        private static void CreateProgrammaticConfiguration(LoggerConfiguration config)
        {
            var configuration = new LoggingConfiguration();

            // 控制台目标
            if (config.EnableConsole)
            {
                var consoleTarget = new NLog.Targets.ConsoleTarget();
                consoleTarget.Name = "console";
                consoleTarget.Layout = "${longdate} ${uppercase:${level}} ${logger:shortName=true} ${message} ${exception:format=tostring}";
                configuration.AddTarget(consoleTarget);
                configuration.AddRule(GetNLogLevel(config.ConsoleMinLevel), NLog.LogLevel.Fatal, consoleTarget);
            }

            // 文件目标
            if (config.EnableFile)
            {
                // 日常日志文件
                var dailyFileTarget = new NLog.Targets.FileTarget();
                dailyFileTarget.Name = "dailyfile";
                dailyFileTarget.FileName = config.LogDirectory + "/daily/${shortdate}/daily.log";
                dailyFileTarget.Layout = "${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}";
                dailyFileTarget.ArchiveFileName = config.LogDirectory + "/daily/${shortdate}/daily-{#}.log";
                dailyFileTarget.ArchiveEvery = NLog.Targets.FileArchivePeriod.Day;
                dailyFileTarget.ArchiveAboveSize = config.ArchiveAboveSize;
                dailyFileTarget.MaxArchiveFiles = config.MaxArchiveFiles;
                dailyFileTarget.KeepFileOpen = false;
                dailyFileTarget.CreateDirs = true;

                configuration.AddTarget(dailyFileTarget);
                configuration.AddRule(NLog.LogLevel.Trace, NLog.LogLevel.Warn, dailyFileTarget);

                // 错误日志文件
                var errorFileTarget = new NLog.Targets.FileTarget();
                errorFileTarget.Name = "errorfile";
                errorFileTarget.FileName = config.LogDirectory + "/error/${shortdate}/error.log";
                errorFileTarget.Layout = "${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}";
                errorFileTarget.ArchiveFileName = config.LogDirectory + "/error/${shortdate}/error-{#}.log";
                errorFileTarget.ArchiveEvery = NLog.Targets.FileArchivePeriod.Day;
                errorFileTarget.ArchiveAboveSize = config.ArchiveAboveSize;
                errorFileTarget.MaxArchiveFiles = config.MaxArchiveFiles;
                errorFileTarget.KeepFileOpen = false;
                errorFileTarget.CreateDirs = true;

                configuration.AddTarget(errorFileTarget);
                configuration.AddRule(NLog.LogLevel.Error, NLog.LogLevel.Fatal, errorFileTarget);
            }

            LogManager.Configuration = configuration;
        }

        /// <summary>
        /// 转换日志级别
        /// </summary>
        /// <param name="level">自定义日志级别</param>
        /// <returns>NLog日志级别</returns>
        private static NLog.LogLevel GetNLogLevel(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Trace:
                    return NLog.LogLevel.Trace;
                case LogLevel.Debug:
                    return NLog.LogLevel.Debug;
                case LogLevel.Info:
                    return NLog.LogLevel.Info;
                case LogLevel.Warn:
                    return NLog.LogLevel.Warn;
                case LogLevel.Error:
                    return NLog.LogLevel.Error;
                case LogLevel.Fatal:
                    return NLog.LogLevel.Fatal;
                default:
                    return NLog.LogLevel.Info;
            }
        }

        public void Trace(string message, params object[] args)
        {
            _logger.Trace(message, args);
        }

        public void Debug(string message, params object[] args)
        {
            _logger.Debug(message, args);
        }

        public void Info(string message, params object[] args)
        {
            _logger.Info(message, args);
        }

        public void Warn(string message, params object[] args)
        {
            _logger.Warn(message, args);
        }

        public void Error(string message, params object[] args)
        {
            _logger.Error(message, args);
        }

        public void Error(Exception exception, string message, params object[] args)
        {
            _logger.Error(exception, message, args);
        }

        public void Fatal(string message, params object[] args)
        {
            _logger.Fatal(message, args);
        }

        public void Fatal(Exception exception, string message, params object[] args)
        {
            _logger.Fatal(exception, message, args);
        }

        public bool IsEnabled(LogLevel level)
        {
            return _logger.IsEnabled(GetNLogLevel(level));
        }
    }
}
