using System;
using System.Linq.Expressions;

namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// Not规约
    /// </summary>
    internal class NotSpecification<T> : BaseSpecification<T>
    {
        public NotSpecification(ISpecification<T> specification)
        {
            if (specification.Criteria != null)
            {
                var parameter = Expression.Parameter(typeof(T));
                var expression = ReplaceParameter(specification.Criteria, parameter);
                var notExpression = Expression.Not(expression.Body);
                AddCriteria(Expression.Lambda<Func<T, bool>>(notExpression, parameter));
            }
        }

        private static Expression<Func<T, bool>> ReplaceParameter(Expression<Func<T, bool>> expression, ParameterExpression parameter)
        {
            return Expression.Lambda<Func<T, bool>>(
                new ParameterReplacer(expression.Parameters[0], parameter).Visit(expression.Body),
                parameter);
        }
    }
}
