<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="logs/internal-nlog.txt">

  <!-- 定义变量 -->
  <variable name="logDirectory" value="logs" />
  <variable name="dailyLogDirectory" value="${logDirectory}/daily/${shortdate}" />
  <variable name="errorLogDirectory" value="${logDirectory}/error/${shortdate}" />

  <!-- 定义目标 -->
  <targets>
    <!-- 控制台输出 -->
    <target xsi:type="Console"
            name="console"
            layout="${longdate} ${uppercase:${level}} ${logger:shortName=true} ${message} ${exception:format=tostring}" />

    <!-- 日常日志文件输出 (<PERSON>, Debug, Info, Warn) -->
    <target xsi:type="File"
            name="dailyfile"
            fileName="${dailyLogDirectory}/daily.log"
            layout="${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="${dailyLogDirectory}/daily-{#}.log"
            archiveEvery="Day"
            archiveAboveSize="10485760"
            archiveNumbering="Rolling"
            maxArchiveFiles="20"
            archiveDateFormat="yyyy-MM-dd"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true"
            enableArchiveFileCompression="false"
            deleteOldFileOnStartup="false" />

    <!-- 错误日志文件输出 (Error, Fatal) -->
    <target xsi:type="File"
            name="errorfile"
            fileName="${errorLogDirectory}/error.log"
            layout="${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="${errorLogDirectory}/error-{#}.log"
            archiveEvery="Day"
            archiveAboveSize="10485760"
            archiveNumbering="Rolling"
            maxArchiveFiles="20"
            archiveDateFormat="yyyy-MM-dd"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true"
            enableArchiveFileCompression="false"
            deleteOldFileOnStartup="false" />
  </targets>

  <!-- 定义规则 -->
  <rules>
    <!-- 错误及致命错误写入到错误日志文件 -->
    <logger name="*" levels="Error,Fatal" writeTo="errorfile" />

    <!-- 日常日志(除错误外)写入到日常日志文件 -->
    <logger name="*" levels="Trace,Debug,Info,Warn" writeTo="dailyfile" />

    <!-- 控制台输出Info及以上级别 -->
    <logger name="*" minlevel="Info" writeTo="console" />

    <!-- 跳过Microsoft的日志，避免过多噪音 -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />
  </rules>
</nlog>
