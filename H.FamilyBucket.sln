﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36221.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ORM", "ORM", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Logging", "Logging", "{6A371E4B-BA6F-4B0C-B24B-925BA1619DC0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Logging.NLogger", "Logging\H.FamilyBucket.Logging.NLogger\H.FamilyBucket.Logging.NLogger.csproj", "{7957C081-1033-4452-96A5-62C813426205}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Repository.SqlSugar.PgSql", "ORM\H.FamilyBucket.Repository.SqlSugar.PgSql\H.FamilyBucket.Repository.SqlSugar.PgSql.csproj", "{9F4DD55B-3506-4C7C-95CF-4FB20D294676}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Repository", "ORM\H.FamilyBucket.Repository\H.FamilyBucket.Repository.csproj", "{A6E94038-CE5B-0C29-7DA2-501A3521D3CD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Repository.SqlSugar", "ORM\H.FamilyBucket.Repository.SqlSugar\H.FamilyBucket.Repository.SqlSugar.csproj", "{F919FC11-EFFC-2065-063B-C04B78446E32}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Repository.SqlSugar.MySql", "ORM\H.FamilyBucket.Repository.SqlSugar.MySql\H.FamilyBucket.Repository.SqlSugar.MySql.csproj", "{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "H.FamilyBucket.Repository.SqlSugar.SqlServer", "ORM\H.FamilyBucket.Repository.SqlSugar.SqlServer\H.FamilyBucket.Repository.SqlSugar.SqlServer.csproj", "{7752AF23-4567-5E27-CC2B-DFA0D88D8878}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7957C081-1033-4452-96A5-62C813426205}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7957C081-1033-4452-96A5-62C813426205}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7957C081-1033-4452-96A5-62C813426205}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7957C081-1033-4452-96A5-62C813426205}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F4DD55B-3506-4C7C-95CF-4FB20D294676}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F4DD55B-3506-4C7C-95CF-4FB20D294676}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F4DD55B-3506-4C7C-95CF-4FB20D294676}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F4DD55B-3506-4C7C-95CF-4FB20D294676}.Release|Any CPU.Build.0 = Release|Any CPU
		{A6E94038-CE5B-0C29-7DA2-501A3521D3CD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A6E94038-CE5B-0C29-7DA2-501A3521D3CD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A6E94038-CE5B-0C29-7DA2-501A3521D3CD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A6E94038-CE5B-0C29-7DA2-501A3521D3CD}.Release|Any CPU.Build.0 = Release|Any CPU
		{F919FC11-EFFC-2065-063B-C04B78446E32}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F919FC11-EFFC-2065-063B-C04B78446E32}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F919FC11-EFFC-2065-063B-C04B78446E32}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F919FC11-EFFC-2065-063B-C04B78446E32}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7752AF23-4567-5E27-CC2B-DFA0D88D8878}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7752AF23-4567-5E27-CC2B-DFA0D88D8878}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7752AF23-4567-5E27-CC2B-DFA0D88D8878}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7752AF23-4567-5E27-CC2B-DFA0D88D8878}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7957C081-1033-4452-96A5-62C813426205} = {6A371E4B-BA6F-4B0C-B24B-925BA1619DC0}
		{9F4DD55B-3506-4C7C-95CF-4FB20D294676} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A6E94038-CE5B-0C29-7DA2-501A3521D3CD} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F919FC11-EFFC-2065-063B-C04B78446E32} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C5CC9BD4-E409-4AB0-F1DB-1DD7D3018DA1} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{7752AF23-4567-5E27-CC2B-DFA0D88D8878} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2E2695AA-9248-4E47-AF26-81492127DCE3}
	EndGlobalSection
EndGlobal
