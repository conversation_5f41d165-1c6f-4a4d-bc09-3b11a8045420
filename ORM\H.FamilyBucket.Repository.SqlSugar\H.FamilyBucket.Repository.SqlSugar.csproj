﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Version>1.0.0</Version>
    <Authors>H.FamilyBucket</Authors>
    <Description>Enhanced SqlSugar repository implementation with multi-database support, distributed transactions, and rich SQL operations</Description>
    <PackageTags>SqlSugar;Repository;ORM;MultiDatabase;DistributedTransaction</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SqlSugarCore" Version="*********" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\H.FamilyBucket.Repository\H.FamilyBucket.Repository.csproj" />
  </ItemGroup>

</Project>
