using System;
using System.ComponentModel.DataAnnotations;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 软删除实体基类
    /// </summary>
    /// <typeparam name="TK<PERSON>">主键类型</typeparam>
    public abstract class SoftDeleteEntity<TKey> : AuditableEntity<TKey>, ISoftDeleteEntity
    {
        /// <summary>
        /// 是否已删除
        /// </summary>
        public virtual bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public virtual DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        [MaxLength(256)]
        public virtual string? DeletedBy { get; set; }
    }

    /// <summary>
    /// 软删除实体基类（long主键）
    /// </summary>
    public abstract class SoftDeleteEntity : SoftDeleteEntity<long>
    {
    }
}
