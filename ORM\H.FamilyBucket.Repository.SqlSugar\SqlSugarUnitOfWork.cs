using System;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.Repositories;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar工作单元实现
    /// </summary>
    public class SqlSugarUnitOfWork : BaseUnitOfWork
    {
        protected readonly ISqlSugarDbContextFactory _dbContextFactory;
        private readonly string? _databaseKey;
        private SqlSugarClient? _client;

        public SqlSugarUnitOfWork(ISqlSugarDbContextFactory dbContextFactory, string? databaseKey = null)
        {
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _databaseKey = databaseKey;
        }

        /// <summary>
        /// 获取SqlSugar客户端
        /// </summary>
        protected SqlSugarClient GetClient()
        {
            return _client ??= string.IsNullOrEmpty(_databaseKey) 
                ? _dbContextFactory.GetClient() 
                : _dbContextFactory.GetClient(_databaseKey);
        }

        /// <summary>
        /// 创建仓储实例
        /// </summary>
        protected override IRepository<TEntity, TKey> CreateRepository<TEntity, TKey>() where TEntity : class
        {
            // 使用反射创建实例，因为基类约束不包含 new()
            return (IRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _databaseKey)!;
        }

        /// <summary>
        /// 创建只读仓储实例
        /// </summary>
        protected override IReadOnlyRepository<TEntity, TKey> CreateReadOnlyRepository<TEntity, TKey>() where TEntity : class
        {
            return (IReadOnlyRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _databaseKey)!;
        }

        /// <summary>
        /// 创建高级仓储实例
        /// </summary>
        protected override IAdvancedRepository<TEntity, TKey> CreateAdvancedRepository<TEntity, TKey>() where TEntity : class
        {
            return (IAdvancedRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _databaseKey)!;
        }

        /// <summary>
        /// 开始事务的内部实现
        /// </summary>
        protected override async Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken)
        {
            var client = GetClient();
            await client.Ado.BeginTranAsync();
            return new SqlSugarDbTransaction(client);
        }

        /// <summary>
        /// 保存更改的内部实现
        /// </summary>
        protected override async Task<int> SaveChangesInternalAsync(CancellationToken cancellationToken)
        {
            // SqlSugar不需要显式保存更改，操作会立即执行
            return await Task.FromResult(0);
        }

        /// <summary>
        /// 释放资源的内部实现
        /// </summary>
        protected override void DisposeInternal()
        {
            _client?.Dispose();
        }
    }
}
