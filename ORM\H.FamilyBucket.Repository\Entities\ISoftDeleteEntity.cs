using System;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 软删除实体接口
    /// </summary>
    public interface ISoftDeleteEntity
    {
        /// <summary>
        /// 是否已删除
        /// </summary>
        bool IsDeleted { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        string? DeletedBy { get; set; }
    }
}
