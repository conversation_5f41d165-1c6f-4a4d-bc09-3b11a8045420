using System;
using System.Linq.Expressions;

namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// Or规约
    /// </summary>
    internal class OrSpecification<T> : BaseSpecification<T>
    {
        public OrSpecification(ISpecification<T> left, ISpecification<T> right)
        {
            if (left.Criteria != null && right.Criteria != null)
            {
                var parameter = Expression.Parameter(typeof(T));
                var leftExpression = ReplaceParameter(left.Criteria, parameter);
                var rightExpression = ReplaceParameter(right.Criteria, parameter);
                var orExpression = Expression.OrElse(leftExpression.Body, rightExpression.Body);
                AddCriteria(Expression.Lambda<Func<T, bool>>(orExpression, parameter));
            }
        }

        private static Expression<Func<T, bool>> ReplaceParameter(Expression<Func<T, bool>> expression, ParameterExpression parameter)
        {
            return Expression.Lambda<Func<T, bool>>(
                new ParameterReplacer(expression.Parameters[0], parameter).Visit(expression.Body),
                parameter);
        }
    }
}
