using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库上下文基类
    /// </summary>
    public abstract class BaseDbContext : IDbContext
    {
        private readonly ConcurrentDictionary<Type, object> _dbSets = new();
        private bool _disposed = false;
        protected IDbTransaction? _currentTransaction;

        protected BaseDbContext(IDbConfiguration configuration)
        {
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// 数据库配置
        /// </summary>
        protected IDbConfiguration Configuration { get; }

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public virtual string ConnectionString => Configuration.ConnectionString;

        /// <summary>
        /// 数据库类型
        /// </summary>
        public virtual string DatabaseType => Configuration.DatabaseType;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public abstract bool IsConnected { get; }

        /// <summary>
        /// 开始事务
        /// </summary>
        public virtual async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_currentTransaction != null)
                throw new InvalidOperationException("A transaction is already in progress");

            _currentTransaction = await BeginTransactionInternalAsync(cancellationToken);
            return _currentTransaction;
        }

        /// <summary>
        /// 保存更改
        /// </summary>
        public abstract Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行SQL命令
        /// </summary>
        public abstract Task<int> ExecuteAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询数据
        /// </summary>
        public abstract Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询单个值
        /// </summary>
        public abstract Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取实体集合
        /// </summary>
        public virtual IDbSet<TEntity> Set<TEntity>() where TEntity : class
        {
            var entityType = typeof(TEntity);
            return (IDbSet<TEntity>)_dbSets.GetOrAdd(entityType, _ => CreateDbSet<TEntity>());
        }

        /// <summary>
        /// 添加实体
        /// </summary>
        public virtual async Task<TEntity> AddAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class
        {
            return await Set<TEntity>().AddAsync(entity, cancellationToken);
        }

        /// <summary>
        /// 添加多个实体
        /// </summary>
        public virtual async Task AddRangeAsync<TEntity>(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default) where TEntity : class
        {
            await Set<TEntity>().AddRangeAsync(entities, cancellationToken);
        }

        /// <summary>
        /// 更新实体
        /// </summary>
        public virtual void Update<TEntity>(TEntity entity) where TEntity : class
        {
            Set<TEntity>().Update(entity);
        }

        /// <summary>
        /// 更新多个实体
        /// </summary>
        public virtual void UpdateRange<TEntity>(IEnumerable<TEntity> entities) where TEntity : class
        {
            Set<TEntity>().UpdateRange(entities);
        }

        /// <summary>
        /// 删除实体
        /// </summary>
        public virtual void Remove<TEntity>(TEntity entity) where TEntity : class
        {
            Set<TEntity>().Remove(entity);
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        public virtual void RemoveRange<TEntity>(IEnumerable<TEntity> entities) where TEntity : class
        {
            Set<TEntity>().RemoveRange(entities);
        }

        /// <summary>
        /// 检查实体是否存在
        /// </summary>
        public virtual async Task<bool> ExistsAsync<TEntity>(object id, CancellationToken cancellationToken = default) where TEntity : class
        {
            var entity = await Set<TEntity>().FindAsync(id, cancellationToken);
            return entity != null;
        }

        /// <summary>
        /// 重新加载实体
        /// </summary>
        public abstract Task ReloadAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class;

        /// <summary>
        /// 分离实体
        /// </summary>
        public virtual void Detach<TEntity>(TEntity entity) where TEntity : class
        {
            Set<TEntity>().Detach(entity);
        }

        /// <summary>
        /// 附加实体
        /// </summary>
        public virtual void Attach<TEntity>(TEntity entity) where TEntity : class
        {
            Set<TEntity>().Attach(entity);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            if (!_disposed)
            {
                _currentTransaction?.Dispose();
                _dbSets.Clear();
                DisposeInternal();
                _disposed = true;
            }
        }

        #region 抽象方法

        /// <summary>
        /// 开始事务的内部实现
        /// </summary>
        protected abstract Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken);

        /// <summary>
        /// 创建数据库集合
        /// </summary>
        protected abstract IDbSet<TEntity> CreateDbSet<TEntity>() where TEntity : class;

        /// <summary>
        /// 释放资源的内部实现
        /// </summary>
        protected virtual void DisposeInternal()
        {
            // 子类可以重写此方法来释放特定资源
        }

        #endregion
    }
}
