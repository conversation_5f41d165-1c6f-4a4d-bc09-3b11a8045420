using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.SqlSugar;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar.SqlServer
{
    /// <summary>
    /// SQL Server特定的仓储实现
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TKey">主键类型</typeparam>
    public class SqlServerRepository<TEntity, TKey> : EnhancedSqlSugarRepository<TEntity, TKey> 
        where TEntity : class, new()
    {
        private readonly SqlServerDatabaseConfig _sqlServerConfig;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContextFactory">数据库上下文工厂</param>
        /// <param name="config">SQL Server配置</param>
        public SqlServerRepository(ISqlSugarDbContextFactory dbContextFactory, SqlServerDatabaseConfig config) 
            : base(dbContextFactory, config.Key)
        {
            _sqlServerConfig = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 构造函数（使用数据库键）
        /// </summary>
        /// <param name="dbContextFactory">数据库上下文工厂</param>
        /// <param name="databaseKey">数据库键</param>
        public SqlServerRepository(ISqlSugarDbContextFactory dbContextFactory, string? databaseKey = null) 
            : base(dbContextFactory, databaseKey)
        {
            _sqlServerConfig = new SqlServerDatabaseConfig("default", "");
        }

        /// <summary>
        /// SQL Server特定的批量插入（使用SqlBulkCopy）
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>插入的行数</returns>
        public virtual async Task<int> SqlServerBulkInsertAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var entityList = entities.ToList();
            
            if (!entityList.Any())
                return 0;

            // 使用SqlSugar的Fastest API进行批量插入
            return await client.Fastest<TEntity>().BulkCopyAsync(entityList);
        }

        /// <summary>
        /// SQL Server特定的批量更新
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新的行数</returns>
        public virtual async Task<int> SqlServerBulkUpdateAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var entityList = entities.ToList();
            
            if (!entityList.Any())
                return 0;

            return await client.Fastest<TEntity>().BulkUpdateAsync(entityList);
        }

        /// <summary>
        /// 执行SQL Server存储过程
        /// </summary>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="parameters">参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public virtual async Task<int> ExecuteStoredProcedureAsync(string procedureName, object? parameters = null, CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            return await client.Ado.UseStoredProcedure().ExecuteCommandAsync(procedureName, parameters);
        }

        /// <summary>
        /// 执行SQL Server存储过程并返回结果集
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="parameters">参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>结果集</returns>
        public virtual async Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string procedureName, object? parameters = null, CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            return await client.Ado.UseStoredProcedure().SqlQueryAsync<T>(procedureName, parameters);
        }

        /// <summary>
        /// 使用表值参数执行存储过程
        /// </summary>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="tableValuedParameters">表值参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public virtual async Task<int> ExecuteStoredProcedureWithTableValuedParametersAsync(
            string procedureName, 
            Dictionary<string, DataTable> tableValuedParameters, 
            CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            
            // 构建参数
            var parameters = new List<SugarParameter>();
            foreach (var tvp in tableValuedParameters)
            {
                parameters.Add(new SugarParameter(tvp.Key, tvp.Value) { IsTableValuedParameter = true });
            }

            return await client.Ado.UseStoredProcedure().ExecuteCommandAsync(procedureName, parameters);
        }

        /// <summary>
        /// 获取SQL Server版本信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>版本信息</returns>
        public virtual async Task<string> GetSqlServerVersionAsync(CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            return await client.Ado.GetStringAsync("SELECT @@VERSION");
        }

        /// <summary>
        /// 获取数据库大小信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数据库大小信息</returns>
        public virtual async Task<dynamic> GetDatabaseSizeInfoAsync(CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = @"
                SELECT 
                    DB_NAME() AS DatabaseName,
                    SUM(CASE WHEN type = 0 THEN size END) * 8 / 1024 AS DataFileSizeMB,
                    SUM(CASE WHEN type = 1 THEN size END) * 8 / 1024 AS LogFileSizeMB,
                    SUM(size) * 8 / 1024 AS TotalSizeMB
                FROM sys.database_files";
            
            return await client.Ado.SqlQuerySingleAsync<dynamic>(sql);
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="schemaName">架构名（默认为dbo）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否存在</returns>
        public virtual async Task<bool> TableExistsAsync(string tableName, string schemaName = "dbo", CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName";
            
            var count = await client.Ado.GetIntAsync(sql, new { schemaName, tableName });
            return count > 0;
        }

        /// <summary>
        /// 获取表的行数统计
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="schemaName">架构名（默认为dbo）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>行数</returns>
        public virtual async Task<long> GetTableRowCountAsync(string tableName, string schemaName = "dbo", CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = $"SELECT COUNT(*) FROM [{schemaName}].[{tableName}]";
            return await client.Ado.GetLongAsync(sql);
        }

        /// <summary>
        /// 重建索引
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="schemaName">架构名（默认为dbo）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public virtual async Task<int> RebuildIndexesAsync(string tableName, string schemaName = "dbo", CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = $"ALTER INDEX ALL ON [{schemaName}].[{tableName}] REBUILD";
            return await client.Ado.ExecuteCommandAsync(sql);
        }

        /// <summary>
        /// 更新表统计信息
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="schemaName">架构名（默认为dbo）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public virtual async Task<int> UpdateStatisticsAsync(string tableName, string schemaName = "dbo", CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = $"UPDATE STATISTICS [{schemaName}].[{tableName}]";
            return await client.Ado.ExecuteCommandAsync(sql);
        }
    }
}
