using Microsoft.Extensions.DependencyInjection;
using SqlSugar;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.Context;
using System;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar依赖注入扩展
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加SqlSugar多数据库支持
        /// </summary>
        public static IServiceCollection AddSqlSugar(this IServiceCollection services, Action<SqlSugarOptions> configure)
        {
            var options = new SqlSugarOptions();
            configure(options);

            // 添加基础仓储支持
            services.AddRepository();

            // 注册多数据库管理器
            services.AddSingleton(provider =>
            {
                var manager = new MultiDatabaseManager();
                foreach (var config in options.Databases)
                {
                    manager.AddDatabase(config);
                }
                if (!string.IsNullOrEmpty(options.DefaultDatabase))
                {
                    manager.SetDefaultDatabase(options.DefaultDatabase);
                }
                return manager;
            });

            // 注册数据库配置
            foreach (var config in options.Databases)
            {
                services.AddDbConfiguration(config);
            }

            // 注册数据库上下文工厂
            services.AddScoped<ISqlSugarDbContextFactory, SqlSugarDbContextFactory>();

            // 注册SqlSugar数据库上下文
            services.AddScoped<SqlSugarDbContext>();

            // 注册工作单元
            services.AddUnitOfWork<SqlSugarUnitOfWork>();
            services.AddDistributedUnitOfWork<SqlSugarDistributedUnitOfWork>();

            // 注册仓储实现
            services.AddScoped(typeof(IRepository<,>), typeof(EnhancedSqlSugarRepository<,>));
            services.AddScoped(typeof(IReadOnlyRepository<,>), typeof(EnhancedSqlSugarRepository<,>));
            services.AddScoped(typeof(IAdvancedRepository<,>), typeof(EnhancedSqlSugarRepository<,>));

            return services;
        }

        /// <summary>
        /// 添加SqlSugar单数据库支持
        /// </summary>
        public static IServiceCollection AddSqlSugar(this IServiceCollection services, string connectionString, DbType dbType = DbType.MySql)
        {
            return services.AddSqlSugar(options =>
            {
                options.AddDatabase(new SqlSugarDatabaseConfig("default", connectionString, dbType));
            });
        }

        /// <summary>
        /// 添加SqlSugar单数据库支持（使用配置）
        /// </summary>
        public static IServiceCollection AddSqlSugar(this IServiceCollection services, SqlSugarDatabaseConfig config)
        {
            return services.AddSqlSugar(options =>
            {
                options.AddDatabase(config);
            });
        }

        /// <summary>
        /// 添加特定数据库的仓储
        /// </summary>
        public static IServiceCollection AddSqlSugarRepository<TEntity, TKey>(
            this IServiceCollection services, 
            string? databaseKey = null)
            where TEntity : class, new()
        {
            services.AddScoped<IRepository<TEntity, TKey>>(provider =>
            {
                var factory = provider.GetRequiredService<ISqlSugarDbContextFactory>();
                return new EnhancedSqlSugarRepository<TEntity, TKey>(factory, databaseKey);
            });

            return services;
        }

        /// <summary>
        /// 添加特定数据库的仓储（自定义实现）
        /// </summary>
        public static IServiceCollection AddSqlSugarRepository<TInterface, TImplementation>(
            this IServiceCollection services,
            string? databaseKey = null,
            ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TInterface : class
            where TImplementation : class, TInterface
        {
            services.Add(new ServiceDescriptor(typeof(TInterface), provider =>
            {
                var factory = provider.GetRequiredService<ISqlSugarDbContextFactory>();
                return ActivatorUtilities.CreateInstance<TImplementation>(provider, factory, databaseKey ?? string.Empty);
            }, lifetime));

            return services;
        }

        /// <summary>
        /// 添加SqlSugar客户端（单例）
        /// </summary>
        public static IServiceCollection AddSqlSugarClient(this IServiceCollection services, string connectionString, DbType dbType = DbType.MySql)
        {
            services.AddSingleton<SqlSugarClient>(_ => SqlSugarHelper.CreateClient(connectionString, dbType));

            return services;
        }

        /// <summary>
        /// 添加SqlSugar客户端（使用配置）
        /// </summary>
        public static IServiceCollection AddSqlSugarClient(this IServiceCollection services, SqlSugarDatabaseConfig config)
        {
            services.AddSingleton<SqlSugarClient>(_ => SqlSugarHelper.CreateClient(config));

            return services;
        }
    }

    /// <summary>
    /// SqlSugar配置选项
    /// </summary>
    public class SqlSugarOptions
    {
        /// <summary>
        /// 数据库配置列表
        /// </summary>
        public List<SqlSugarDatabaseConfig> Databases { get; } = [];

        /// <summary>
        /// 默认数据库键
        /// </summary>
        public string? DefaultDatabase { get; set; }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        public SqlSugarOptions AddDatabase(SqlSugarDatabaseConfig config)
        {
            Databases.Add(config);
            
            // 如果是第一个数据库且没有设置默认数据库，则设为默认
            if (Databases.Count == 1 && string.IsNullOrEmpty(DefaultDatabase))
            {
                DefaultDatabase = config.Key;
            }
            
            return this;
        }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        public SqlSugarOptions AddDatabase(string key, string connectionString, DbType dbType = DbType.MySql)
        {
            return AddDatabase(new SqlSugarDatabaseConfig(key, connectionString, dbType));
        }

        /// <summary>
        /// 设置默认数据库
        /// </summary>
        public SqlSugarOptions SetDefaultDatabase(string key)
        {
            DefaultDatabase = key;
            return this;
        }
    }
}
