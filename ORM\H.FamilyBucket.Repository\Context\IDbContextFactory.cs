using System.Collections.Generic;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库上下文工厂接口
    /// </summary>
    public interface IDbContextFactory
    {
        /// <summary>
        /// 创建数据库上下文
        /// </summary>
        IDbContext CreateContext();

        /// <summary>
        /// 创建指定数据库的上下文
        /// </summary>
        IDbContext CreateContext(string databaseKey);

        /// <summary>
        /// 获取所有数据库键
        /// </summary>
        IEnumerable<string> GetDatabaseKeys();

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        bool DatabaseExists(string databaseKey);
    }
}
