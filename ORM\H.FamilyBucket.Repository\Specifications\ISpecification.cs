using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// 规约接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public interface ISpecification<T>
    {
        /// <summary>
        /// 查询条件表达式
        /// </summary>
        Expression<Func<T, bool>>? Criteria { get; }

        /// <summary>
        /// 包含的导航属性
        /// </summary>
        List<Expression<Func<T, object>>> Includes { get; }

        /// <summary>
        /// 包含的字符串导航属性
        /// </summary>
        List<string> IncludeStrings { get; }

        /// <summary>
        /// 排序表达式
        /// </summary>
        Expression<Func<T, object>>? OrderBy { get; }

        /// <summary>
        /// 降序排序表达式
        /// </summary>
        Expression<Func<T, object>>? OrderByDescending { get; }

        /// <summary>
        /// 分组表达式
        /// </summary>
        Expression<Func<T, object>>? GroupBy { get; }

        /// <summary>
        /// 是否启用分页
        /// </summary>
        bool IsPagingEnabled { get; }

        /// <summary>
        /// 跳过的记录数
        /// </summary>
        int Skip { get; }

        /// <summary>
        /// 获取的记录数
        /// </summary>
        int Take { get; }

        /// <summary>
        /// 是否启用跟踪
        /// </summary>
        bool IsTrackingEnabled { get; }

        /// <summary>
        /// 是否启用拆分查询
        /// </summary>
        bool IsSplitQueryEnabled { get; }

        /// <summary>
        /// 是否满足规约
        /// </summary>
        bool IsSatisfiedBy(T entity);
    }
}
