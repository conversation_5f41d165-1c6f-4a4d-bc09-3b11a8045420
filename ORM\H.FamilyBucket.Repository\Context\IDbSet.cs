using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库集合接口
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    public interface IDbSet<TEntity> where TEntity : class
    {
        /// <summary>
        /// 添加实体
        /// </summary>
        Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);

        /// <summary>
        /// 添加多个实体
        /// </summary>
        Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新实体
        /// </summary>
        void Update(TEntity entity);

        /// <summary>
        /// 更新多个实体
        /// </summary>
        void UpdateRange(IEnumerable<TEntity> entities);

        /// <summary>
        /// 删除实体
        /// </summary>
        void Remove(TEntity entity);

        /// <summary>
        /// 删除多个实体
        /// </summary>
        void RemoveRange(IEnumerable<TEntity> entities);

        /// <summary>
        /// 根据ID查找实体
        /// </summary>
        Task<TEntity?> FindAsync(object id, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据多个ID查找实体
        /// </summary>
        Task<TEntity?> FindAsync(object[] ids, CancellationToken cancellationToken = default);

        /// <summary>
        /// 附加实体
        /// </summary>
        void Attach(TEntity entity);

        /// <summary>
        /// 分离实体
        /// </summary>
        void Detach(TEntity entity);
    }
}
