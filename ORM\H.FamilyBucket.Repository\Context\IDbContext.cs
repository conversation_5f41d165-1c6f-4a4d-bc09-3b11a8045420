using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库上下文接口
    /// </summary>
    public interface IDbContext : IDisposable
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        string ConnectionString { get; }

        /// <summary>
        /// 数据库类型
        /// </summary>
        string DatabaseType { get; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 开始事务
        /// </summary>
        Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 保存更改
        /// </summary>
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行SQL命令
        /// </summary>
        Task<int> ExecuteAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询数据
        /// </summary>
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询单个值
        /// </summary>
        Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取实体集合
        /// </summary>
        IDbSet<TEntity> Set<TEntity>() where TEntity : class;

        /// <summary>
        /// 添加实体
        /// </summary>
        Task<TEntity> AddAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class;

        /// <summary>
        /// 添加多个实体
        /// </summary>
        Task AddRangeAsync<TEntity>(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default) where TEntity : class;

        /// <summary>
        /// 更新实体
        /// </summary>
        void Update<TEntity>(TEntity entity) where TEntity : class;

        /// <summary>
        /// 更新多个实体
        /// </summary>
        void UpdateRange<TEntity>(IEnumerable<TEntity> entities) where TEntity : class;

        /// <summary>
        /// 删除实体
        /// </summary>
        void Remove<TEntity>(TEntity entity) where TEntity : class;

        /// <summary>
        /// 删除多个实体
        /// </summary>
        void RemoveRange<TEntity>(IEnumerable<TEntity> entities) where TEntity : class;

        /// <summary>
        /// 检查实体是否存在
        /// </summary>
        Task<bool> ExistsAsync<TEntity>(object id, CancellationToken cancellationToken = default) where TEntity : class;

        /// <summary>
        /// 重新加载实体
        /// </summary>
        Task ReloadAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class;

        /// <summary>
        /// 分离实体
        /// </summary>
        void Detach<TEntity>(TEntity entity) where TEntity : class;

        /// <summary>
        /// 附加实体
        /// </summary>
        void Attach<TEntity>(TEntity entity) where TEntity : class;
    }
}
