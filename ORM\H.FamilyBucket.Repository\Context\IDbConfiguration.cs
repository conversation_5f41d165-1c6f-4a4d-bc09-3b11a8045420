using System;
using System.Collections.Generic;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库配置接口
    /// </summary>
    public interface IDbConfiguration
    {
        /// <summary>
        /// 数据库键
        /// </summary>
        string Key { get; }

        /// <summary>
        /// 连接字符串
        /// </summary>
        string ConnectionString { get; }

        /// <summary>
        /// 数据库类型
        /// </summary>
        string DatabaseType { get; }

        /// <summary>
        /// 超时时间
        /// </summary>
        int CommandTimeout { get; }

        /// <summary>
        /// 是否启用日志
        /// </summary>
        bool EnableLogging { get; }

        /// <summary>
        /// 是否启用敏感数据日志
        /// </summary>
        bool EnableSensitiveDataLogging { get; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        int MaxRetryCount { get; }

        /// <summary>
        /// 重试延迟
        /// </summary>
        TimeSpan RetryDelay { get; }

        /// <summary>
        /// 连接池大小
        /// </summary>
        int PoolSize { get; }

        /// <summary>
        /// 是否启用连接池
        /// </summary>
        bool EnableConnectionPooling { get; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        Dictionary<string, object> Properties { get; }
    }
}
