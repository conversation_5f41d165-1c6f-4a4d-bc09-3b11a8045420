using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// 规约基类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class BaseSpecification<T> : ISpecification<T>
    {
        protected BaseSpecification()
        {
            Includes = new List<Expression<Func<T, object>>>();
            IncludeStrings = new List<string>();
        }

        protected BaseSpecification(Expression<Func<T, bool>> criteria) : this()
        {
            Criteria = criteria;
        }

        public Expression<Func<T, bool>>? Criteria { get; private set; }
        public List<Expression<Func<T, object>>> Includes { get; }
        public List<string> IncludeStrings { get; }
        public Expression<Func<T, object>>? OrderBy { get; private set; }
        public Expression<Func<T, object>>? OrderByDescending { get; private set; }
        public Expression<Func<T, object>>? GroupBy { get; private set; }
        public bool IsPagingEnabled { get; private set; }
        public int Skip { get; private set; }
        public int Take { get; private set; }
        public bool IsTrackingEnabled { get; private set; } = true;
        public bool IsSplitQueryEnabled { get; private set; }

        /// <summary>
        /// 添加查询条件
        /// </summary>
        protected virtual void AddCriteria(Expression<Func<T, bool>> criteria)
        {
            Criteria = criteria;
        }

        /// <summary>
        /// 添加包含的导航属性
        /// </summary>
        protected virtual void AddInclude(Expression<Func<T, object>> includeExpression)
        {
            Includes.Add(includeExpression);
        }

        /// <summary>
        /// 添加包含的字符串导航属性
        /// </summary>
        protected virtual void AddInclude(string includeString)
        {
            IncludeStrings.Add(includeString);
        }

        /// <summary>
        /// 添加排序
        /// </summary>
        protected virtual void AddOrderBy(Expression<Func<T, object>> orderByExpression)
        {
            OrderBy = orderByExpression;
        }

        /// <summary>
        /// 添加降序排序
        /// </summary>
        protected virtual void AddOrderByDescending(Expression<Func<T, object>> orderByDescExpression)
        {
            OrderByDescending = orderByDescExpression;
        }

        /// <summary>
        /// 添加分组
        /// </summary>
        protected virtual void AddGroupBy(Expression<Func<T, object>> groupByExpression)
        {
            GroupBy = groupByExpression;
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        protected virtual void ApplyPaging(int skip, int take)
        {
            Skip = skip;
            Take = take;
            IsPagingEnabled = true;
        }

        /// <summary>
        /// 禁用跟踪
        /// </summary>
        protected virtual void DisableTracking()
        {
            IsTrackingEnabled = false;
        }

        /// <summary>
        /// 启用拆分查询
        /// </summary>
        protected virtual void EnableSplitQuery()
        {
            IsSplitQueryEnabled = true;
        }

        /// <summary>
        /// 是否满足规约
        /// </summary>
        public virtual bool IsSatisfiedBy(T entity)
        {
            return Criteria?.Compile().Invoke(entity) ?? true;
        }
    }
}
