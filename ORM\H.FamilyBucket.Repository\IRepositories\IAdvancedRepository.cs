using System;
using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.Specifications;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 高级仓储接口，提供分页、批量操作、动态查询等功能
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TKey">主键类型</typeparam>
    public interface IAdvancedRepository<TEntity, TKey> : IRepository<TEntity, TKey> where TEntity : class
    {
        #region 分页查询

        /// <summary>
        /// 分页查询
        /// </summary>
        Task<IPagedResult<TEntity>> GetPagedAsync(
            int pageIndex, 
            int pageSize, 
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 分页查询（带排序）
        /// </summary>
        Task<IPagedResult<TEntity>> GetPagedAsync<TOrderBy>(
            int pageIndex, 
            int pageSize, 
            Expression<Func<TEntity, bool>>? predicate = null,
            Expression<Func<TEntity, TOrderBy>>? orderBy = null,
            bool ascending = true,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// SQL分页查询
        /// </summary>
        Task<IPagedResult<T>> GetPagedAsync<T>(
            string sql, 
            int pageIndex, 
            int pageSize, 
            object? parameters = null,
            CancellationToken cancellationToken = default);

        #endregion

        #region 批量操作

        /// <summary>
        /// 批量插入
        /// </summary>
        Task<int> BulkInsertAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量更新
        /// </summary>
        Task<int> BulkUpdateAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除
        /// </summary>
        Task<int> BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除
        /// </summary>
        Task<int> BulkDeleteAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        #endregion

        #region 高级查询

        /// <summary>
        /// 获取第一个实体
        /// </summary>
        Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取单个实体
        /// </summary>
        Task<TEntity?> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询并投影
        /// </summary>
        Task<IEnumerable<TResult>> SelectAsync<TResult>(
            Expression<Func<TEntity, TResult>> selector,
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 聚合查询
        /// </summary>
        Task<TResult> AggregateAsync<TResult>(
            Expression<Func<IQueryable<TEntity>, TResult>> aggregateExpression,
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        #endregion

        #region SQL操作扩展

        /// <summary>
        /// 查询动态对象
        /// </summary>
        Task<IEnumerable<dynamic>> QueryDynamicAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询指定类型
        /// </summary>
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询单个值
        /// </summary>
        Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询DataTable
        /// </summary>
        Task<DataTable> QueryDataTableAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        #endregion

        #region 规约模式

        /// <summary>
        /// 根据规约查询
        /// </summary>
        Task<IEnumerable<TEntity>> FindAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据规约查询第一个实体
        /// </summary>
        Task<TEntity?> FirstOrDefaultAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据规约查询单个实体
        /// </summary>
        Task<TEntity?> SingleOrDefaultAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据规约统计数量
        /// </summary>
        Task<int> CountAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据规约检查是否存在
        /// </summary>
        Task<bool> ExistsAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据规约分页查询
        /// </summary>
        Task<IPagedResult<TEntity>> GetPagedAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default);

        #endregion

        #region 异步枚举

        /// <summary>
        /// 异步枚举查询
        /// </summary>
        IAsyncEnumerable<TEntity> FindAsyncEnumerable(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步枚举查询（带排序）
        /// </summary>
        new IAsyncEnumerable<TEntity> FindAsyncEnumerable<TOrderBy>(
            Expression<Func<TEntity, bool>>? predicate = null,
            Expression<Func<TEntity, TOrderBy>>? orderBy = null,
            bool ascending = true,
            CancellationToken cancellationToken = default);

        #endregion
    }
}
