using SqlSugar;
using System;
using System.Collections.Generic;
using H.FamilyBucket.Repository.Context;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar数据库配置信息
    /// </summary>
    public class SqlSugarDatabaseConfig : IDbConfiguration
    {
        public SqlSugarDatabaseConfig(string key, string connectionString, DbType dbType = DbType.MySql)
        {
            Key = key ?? throw new ArgumentNullException(nameof(key));
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            DatabaseType = dbType.ToString();
            DbType = dbType;
            Properties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 数据库标识
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; }

        /// <summary>
        /// 数据库类型（字符串）
        /// </summary>
        public string DatabaseType { get; }

        /// <summary>
        /// SqlSugar数据库类型
        /// </summary>
        public DbType DbType { get; set; }

        /// <summary>
        /// 是否自动关闭连接
        /// </summary>
        public bool IsAutoCloseConnection { get; set; } = true;

        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool IsLogSql { get; set; } = false;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int CommandTimeout { get; set; } = 30;

        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = false;

        /// <summary>
        /// 是否启用敏感数据日志
        /// </summary>
        public bool EnableSensitiveDataLogging { get; set; } = false;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟
        /// </summary>
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// 连接池大小
        /// </summary>
        public int PoolSize { get; set; } = 100;

        /// <summary>
        /// 是否启用连接池
        /// </summary>
        public bool EnableConnectionPooling { get; set; } = true;

        /// <summary>
        /// 是否启用读写分离
        /// </summary>
        public bool IsReadWriteSeparation { get; set; } = false;

        /// <summary>
        /// 从库连接字符串列表（读写分离时使用）
        /// </summary>
        public List<string> SlaveConnectionStrings { get; set; } = [];

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; }
    }
}
