using System;
using System.ComponentModel.DataAnnotations;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 完整功能实体基类
    /// </summary>
    /// <typeparam name="TK<PERSON>">主键类型</typeparam>
    public abstract class FullEntity<TKey> : BaseEntity<TKey>, IFullEntity<TKey>
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public virtual DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(256)]
        public virtual string? CreatedBy { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public virtual DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(256)]
        public virtual string? UpdatedBy { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public virtual bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public virtual DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        [MaxLength(256)]
        public virtual string? DeletedBy { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Timestamp]
        public virtual long Version { get; set; }
    }

    /// <summary>
    /// 完整功能实体基类（long主键）
    /// </summary>
    public abstract class FullEntity : FullEntity<long>, IFullEntity
    {
    }
}
