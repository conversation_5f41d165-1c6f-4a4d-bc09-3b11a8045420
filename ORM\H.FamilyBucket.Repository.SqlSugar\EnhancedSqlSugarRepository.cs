using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.Repositories;
using H.FamilyBucket.Repository.Specifications;
using SqlSugar;
using IDbTransaction = H.FamilyBucket.Repository.IRepositories.IDbTransaction;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// 增强的SqlSugar仓储实现，支持多数据库和更多SQL操作
    /// </summary>
    public class EnhancedSqlSugarRepository<TEntity, TKey> : BaseRepository<TEntity, TKey> 
        where TEntity : class, new()
    {
        protected readonly ISqlSugarDbContextFactory _dbContextFactory;
        protected readonly string? _databaseKey;

        public EnhancedSqlSugarRepository(ISqlSugarDbContextFactory dbContextFactory, string? databaseKey = null)
        {
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _databaseKey = databaseKey;
        }

        /// <summary>
        /// 获取数据库客户端
        /// </summary>
        protected virtual SqlSugarClient GetClient()
        {
            return string.IsNullOrEmpty(_databaseKey) 
                ? _dbContextFactory.GetClient() 
                : _dbContextFactory.GetClient(_databaseKey);
        }

        #region 基础CRUD操作

        protected override async Task<TEntity?> GetByIdInternalAsync(TKey id, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Queryable<TEntity>().InSingleAsync(id);
        }

        protected override async Task<IEnumerable<TEntity>> GetAllInternalAsync(CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Queryable<TEntity>().ToListAsync();
        }

        protected override async Task<IEnumerable<TEntity>> FindInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Queryable<TEntity>().Where(predicate).ToListAsync();
        }

        protected override async Task<int> CountInternalAsync(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);
            return await query.CountAsync();
        }

        protected override async Task<bool> ExistsInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Queryable<TEntity>().AnyAsync(predicate);
        }

        protected override async Task<TEntity> AddInternalAsync(TEntity entity, CancellationToken cancellationToken)
        {
            var client = GetClient();
            await client.Insertable(entity).ExecuteCommandAsync();
            return entity;
        }

        protected override async Task<IEnumerable<TEntity>> AddRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            await client.Insertable(entities.ToList()).ExecuteCommandAsync();
            return entities;
        }

        protected override async Task<bool> UpdateInternalAsync(TEntity entity, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var result = await client.Updateable(entity).ExecuteCommandAsync();
            return result > 0;
        }

        protected override async Task<bool> UpdateRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var result = await client.Updateable(entities.ToList()).ExecuteCommandAsync();
            return result > 0;
        }

        protected override async Task<bool> DeleteInternalAsync(TEntity entity, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var result = await client.Deleteable(entity).ExecuteCommandAsync();
            return result > 0;
        }

        protected override async Task<bool> DeleteRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var result = await client.Deleteable(entities.ToList()).ExecuteCommandAsync();
            return result > 0;
        }

        #endregion

        #region SQL执行操作

        protected override async Task<IEnumerable<TEntity>> QueryInternalAsync(string sql, object? parameters, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Ado.SqlQueryAsync<TEntity>(sql, parameters);
        }

        protected override async Task<int> ExecuteInternalAsync(string sql, object? parameters, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Ado.ExecuteCommandAsync(sql, parameters);
        }

        /// <summary>
        /// 执行SQL查询返回动态对象
        /// </summary>
        public virtual async Task<IEnumerable<dynamic>> QueryDynamicAsync(string sql, object? param = null)
        {
            var client = GetClient();
            return await client.Ado.SqlQueryAsync<dynamic>(sql, param);
        }

        /// <summary>
        /// 执行SQL查询返回指定类型
        /// </summary>
        public virtual async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null)
        {
            var client = GetClient();
            return await client.Ado.SqlQueryAsync<T>(sql, param);
        }

        /// <summary>
        /// 执行SQL查询返回单个值
        /// </summary>
        public virtual async Task<T> ExecuteScalarAsync<T>(string sql, object? param = null)
        {
            var client = GetClient();
            var result = await client.Ado.GetScalarAsync(sql, param);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        /// <summary>
        /// 执行SQL查询返回DataTable
        /// </summary>
        public virtual async Task<DataTable> QueryDataTableAsync(string sql, object? param = null)
        {
            var client = GetClient();
            return await client.Ado.GetDataTableAsync(sql, param);
        }

        #endregion

        #region 事务操作

        protected override async Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken)
        {
            var client = GetClient();
            await client.Ado.BeginTranAsync();
            return new SqlSugarDbTransaction(client);
        }

        /// <summary>
        /// 在事务中执行操作
        /// </summary>
        public virtual async Task<T> ExecuteInTransactionAsync<T>(Func<SqlSugarClient, Task<T>> action)
        {
            var client = GetClient();
            try
            {
                await client.Ado.BeginTranAsync();
                var result = await action(client);
                await client.Ado.CommitTranAsync();
                return result;
            }
            catch
            {
                await client.Ado.RollbackTranAsync();
                throw;
            }
        }

        /// <summary>
        /// 在事务中执行操作（无返回值）
        /// </summary>
        public virtual async Task ExecuteInTransactionAsync(Func<SqlSugarClient, Task> action)
        {
            var client = GetClient();
            try
            {
                await client.Ado.BeginTranAsync();
                await action(client);
                await client.Ado.CommitTranAsync();
            }
            catch
            {
                await client.Ado.RollbackTranAsync();
                throw;
            }
        }

        #endregion

        #region 分页查询

        /// <summary>
        /// 分页查询
        /// </summary>
        public virtual async Task<(IEnumerable<TEntity> Data, int Total)> GetPagedAsync(
            int pageIndex, 
            int pageSize, 
            Expression<Func<TEntity, bool>>? predicate = null,
            Expression<Func<TEntity, object>>? orderBy = null,
            bool isAsc = true)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            
            if (predicate != null)
                query = query.Where(predicate);
                
            if (orderBy != null)
                query = isAsc ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);

            RefAsync<int> totalCount = 0;
            var data = await query.ToPageListAsync(pageIndex, pageSize, totalCount);
            
            return (data, totalCount.Value);
        }

        /// <summary>
        /// 分页查询（使用SQL）
        /// </summary>
        public virtual async Task<(IEnumerable<T> Data, int Total)> GetPagedAsync<T>(
            string sql,
            int pageIndex,
            int pageSize,
            object? param = null)
        {
            var client = GetClient();
            // 构建分页SQL
            var countSql = $"SELECT COUNT(*) FROM ({sql}) AS CountTable";
            var totalCount = await client.Ado.GetIntAsync(countSql, param);

            var offset = (pageIndex - 1) * pageSize;
            var pagedSql = $"{sql} LIMIT {pageSize} OFFSET {offset}";
            var data = await client.Ado.SqlQueryAsync<T>(pagedSql, param);

            return (data, totalCount);
        }

        #endregion

        #region 批量操作

        /// <summary>
        /// 批量插入
        /// </summary>
        public virtual async Task<int> BulkInsertAsync(IEnumerable<TEntity> entities)
        {
            var client = GetClient();
            return await client.Fastest<TEntity>().BulkCopyAsync(entities.ToList());
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        public virtual async Task<int> BulkUpdateAsync(IEnumerable<TEntity> entities)
        {
            var client = GetClient();
            return await client.Fastest<TEntity>().BulkUpdateAsync(entities.ToList());
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        public virtual async Task<int> BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate)
        {
            var client = GetClient();
            return await client.Deleteable<TEntity>().Where(predicate).ExecuteCommandAsync();
        }

        #endregion

        #region 多数据库操作

        /// <summary>
        /// 切换到指定数据库执行操作
        /// </summary>
        public virtual async Task<T> ExecuteOnDatabaseAsync<T>(string databaseKey, Func<SqlSugarClient, Task<T>> action)
        {
            var client = _dbContextFactory.GetClient(databaseKey);
            return await action(client);
        }

        /// <summary>
        /// 切换到指定数据库执行操作（无返回值）
        /// </summary>
        public virtual async Task ExecuteOnDatabaseAsync(string databaseKey, Func<SqlSugarClient, Task> action)
        {
            var client = _dbContextFactory.GetClient(databaseKey);
            await action(client);
        }

        /// <summary>
        /// 在多个数据库上执行分布式事务
        /// </summary>
        public virtual async Task<T> ExecuteDistributedTransactionAsync<T>(
            Func<Task<T>> action,
            params string[] databaseKeys)
        {
            using var transactionManager = databaseKeys.Any()
                ? _dbContextFactory.CreateDistributedTransaction(databaseKeys)
                : _dbContextFactory.CreateDistributedTransaction();

            return await transactionManager.ExecuteAsync(action);
        }

        /// <summary>
        /// 在多个数据库上执行分布式事务（无返回值）
        /// </summary>
        public virtual async Task ExecuteDistributedTransactionAsync(
            Func<Task> action,
            params string[] databaseKeys)
        {
            using var transactionManager = databaseKeys.Any()
                ? _dbContextFactory.CreateDistributedTransaction(databaseKeys)
                : _dbContextFactory.CreateDistributedTransaction();

            await transactionManager.ExecuteAsync(action);
        }

        #endregion

        #region 新增的抽象方法实现

        protected override async Task<TEntity?> FirstOrDefaultInternalAsync(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);
            return await query.FirstAsync();
        }

        protected override async Task<TEntity?> SingleOrDefaultInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Queryable<TEntity>().Where(predicate).SingleAsync();
        }

        protected override async Task<IPagedResult<TEntity>> GetPagedInternalAsync(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);

            var totalCount = await query.CountAsync();
            var data = await query.ToPageListAsync(pageIndex, pageSize);

            return new PagedResult<TEntity>(data, totalCount, pageIndex, pageSize);
        }

        protected override async Task<IPagedResult<TEntity>> GetPagedInternalAsync<TOrderBy>(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate, Expression<Func<TEntity, TOrderBy>>? orderBy, bool ascending, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            if (predicate != null)
                query = query.Where(predicate);

            if (orderBy != null)
            {
                // 将泛型表达式转换为object表达式
                var objectOrderBy = Expression.Lambda<Func<TEntity, object>>(
                    Expression.Convert(orderBy.Body, typeof(object)),
                    orderBy.Parameters);
                query = ascending ? query.OrderBy(objectOrderBy) : query.OrderByDescending(objectOrderBy);
            }

            var totalCount = await query.CountAsync();
            var data = await query.ToPageListAsync(pageIndex, pageSize);

            return new PagedResult<TEntity>(data, totalCount, pageIndex, pageSize);
        }

        protected override async Task<IEnumerable<TResult>> SelectInternalAsync<TResult>(Expression<Func<TEntity, TResult>> selector, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);
            return await query.Select(selector).ToListAsync();
        }

        protected override Task<TResult> AggregateInternalAsync<TResult>(Expression<Func<IQueryable<TEntity>, TResult>> aggregateExpression, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);

            // 这里需要根据具体的聚合表达式来实现
            // 这是一个简化的实现，实际使用时可能需要更复杂的逻辑
            throw new NotImplementedException("Aggregate expressions need to be implemented based on specific requirements");
        }

        protected override async Task<int> BulkInsertInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Fastest<TEntity>().BulkCopyAsync(entities.ToList());
        }

        protected override async Task<int> BulkUpdateInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Fastest<TEntity>().BulkUpdateAsync(entities.ToList());
        }

        protected override async Task<int> BulkDeleteInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Deleteable<TEntity>().Where(predicate).ExecuteCommandAsync();
        }

        protected override async Task<int> BulkDeleteInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Deleteable(entities.ToList()).ExecuteCommandAsync();
        }

        protected override async Task<IEnumerable<dynamic>> QueryDynamicInternalAsync(string sql, object? parameters, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Ado.SqlQueryAsync<dynamic>(sql, parameters);
        }

        protected override async Task<IEnumerable<T>> QueryInternalAsync<T>(string sql, object? parameters, CancellationToken cancellationToken)
        {
            var client = GetClient();
            return await client.Ado.SqlQueryAsync<T>(sql, parameters);
        }

        protected override async Task<T> ExecuteScalarInternalAsync<T>(string sql, object? parameters, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var result = await client.Ado.GetScalarAsync(sql, parameters);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        protected override IAsyncEnumerable<TEntity> FindAsyncEnumerableInternal(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();
            if (predicate != null)
                query = query.Where(predicate);

            // SqlSugar doesn't have built-in IAsyncEnumerable support, so we'll simulate it
            return ToAsyncEnumerable(query.ToListAsync());
        }

        protected override IAsyncEnumerable<TEntity> FindAsyncEnumerableInternal<TOrderBy>(Expression<Func<TEntity, bool>>? predicate, Expression<Func<TEntity, TOrderBy>>? orderBy, bool ascending, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            if (predicate != null)
                query = query.Where(predicate);

            if (orderBy != null)
            {
                // 将泛型表达式转换为object表达式
                var objectOrderBy = Expression.Lambda<Func<TEntity, object>>(
                    Expression.Convert(orderBy.Body, typeof(object)),
                    orderBy.Parameters);
                query = ascending ? query.OrderBy(objectOrderBy) : query.OrderByDescending(objectOrderBy);
            }

            return ToAsyncEnumerable(query.ToListAsync());
        }

        private async IAsyncEnumerable<TEntity> ToAsyncEnumerable(Task<List<TEntity>> task)
        {
            var list = await task;
            foreach (var item in list)
            {
                yield return item;
            }
        }

        #endregion

        #region 规约模式实现

        protected override async Task<IEnumerable<TEntity>> FindInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            query = ApplySpecification(query, specification);

            return await query.ToListAsync();
        }

        protected override async Task<TEntity?> FirstOrDefaultInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            query = ApplySpecification(query, specification);

            return await query.FirstAsync();
        }

        protected override async Task<TEntity?> SingleOrDefaultInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            query = ApplySpecification(query, specification);

            return await query.SingleAsync();
        }

        protected override async Task<int> CountInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            if (specification.Criteria != null)
                query = query.Where(specification.Criteria);

            return await query.CountAsync();
        }

        protected override async Task<bool> ExistsInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            if (specification.Criteria != null)
                query = query.Where(specification.Criteria);

            return await query.AnyAsync();
        }

        protected override async Task<IPagedResult<TEntity>> GetPagedInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken)
        {
            var client = GetClient();
            var query = client.Queryable<TEntity>();

            // 应用查询条件
            if (specification.Criteria != null)
                query = query.Where(specification.Criteria);

            // 应用排序
            if (specification.OrderBy != null)
                query = query.OrderBy(specification.OrderBy);
            else if (specification.OrderByDescending != null)
                query = query.OrderByDescending(specification.OrderByDescending);

            // 获取总数
            var totalCount = await query.CountAsync();

            // 应用分页
            if (specification.IsPagingEnabled)
            {
                query = query.Skip(specification.Skip).Take(specification.Take);
            }

            var data = await query.ToListAsync();

            var pageIndex = specification.IsPagingEnabled ? (specification.Skip / specification.Take) + 1 : 1;
            var pageSize = specification.IsPagingEnabled ? specification.Take : totalCount;

            return new PagedResult<TEntity>(data, totalCount, pageIndex, pageSize);
        }

        private ISugarQueryable<TEntity> ApplySpecification(ISugarQueryable<TEntity> query, ISpecification<TEntity> specification)
        {
            // 应用查询条件
            if (specification.Criteria != null)
                query = query.Where(specification.Criteria);

            // 应用排序
            if (specification.OrderBy != null)
                query = query.OrderBy(specification.OrderBy);
            else if (specification.OrderByDescending != null)
                query = query.OrderByDescending(specification.OrderByDescending);

            // 应用分页
            if (specification.IsPagingEnabled)
            {
                query = query.Skip(specification.Skip).Take(specification.Take);
            }

            return query;
        }

        #endregion
    }
}
