using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar分布式工作单元实现
    /// </summary>
    public class SqlSugarDistributedUnitOfWork : SqlSugarUnitOfWork, IDistributedUnitOfWork
    {
        private readonly ConcurrentDictionary<string, object> _contexts = new();
        private DistributedTransactionManager? _distributedTransactionManager;

        public SqlSugarDistributedUnitOfWork(ISqlSugarDbContextFactory dbContextFactory) : base(dbContextFactory)
        {
        }

        /// <summary>
        /// 添加数据库上下文
        /// </summary>
        public void AddContext(string key, object context)
        {
            _contexts.AddOrUpdate(key, context, (k, v) => context);
        }

        /// <summary>
        /// 获取数据库上下文
        /// </summary>
        public T GetContext<T>(string key) where T : class
        {
            if (_contexts.TryGetValue(key, out var context))
            {
                return (T)context;
            }
            throw new InvalidOperationException($"Context with key '{key}' not found");
        }

        /// <summary>
        /// 获取指定数据库的仓储
        /// </summary>
        IRepository<TEntity, TKey> IDistributedUnitOfWork.GetRepository<TEntity, TKey>(string databaseKey) where TEntity : class
        {
            // 这里我们需要使用反射或者工厂模式来创建仓储，因为接口约束不包含new()
            // 为了简化，我们假设TEntity有无参构造函数
            return (IRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, databaseKey)!;
        }

        /// <summary>
        /// 获取指定数据库的只读仓储
        /// </summary>
        IReadOnlyRepository<TEntity, TKey> IDistributedUnitOfWork.GetReadOnlyRepository<TEntity, TKey>(string databaseKey) where TEntity : class
        {
            return (IReadOnlyRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, databaseKey)!;
        }

        /// <summary>
        /// 获取指定数据库的高级仓储
        /// </summary>
        IAdvancedRepository<TEntity, TKey> IDistributedUnitOfWork.GetAdvancedRepository<TEntity, TKey>(string databaseKey) where TEntity : class
        {
            return (IAdvancedRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(EnhancedSqlSugarRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, databaseKey)!;
        }

        /// <summary>
        /// 获取指定数据库的仓储（强类型版本，需要new()约束）
        /// </summary>
        public EnhancedSqlSugarRepository<TEntity, TKey> GetRepository<TEntity, TKey>(string databaseKey) where TEntity : class, new()
        {
            return new EnhancedSqlSugarRepository<TEntity, TKey>(_dbContextFactory, databaseKey);
        }

        /// <summary>
        /// 开始分布式事务
        /// </summary>
        public async Task BeginDistributedTransactionAsync(params string[] databaseKeys)
        {
            _distributedTransactionManager = databaseKeys.Length > 0 
                ? _dbContextFactory.CreateDistributedTransaction(databaseKeys)
                : _dbContextFactory.CreateDistributedTransaction();
                
            await _distributedTransactionManager.BeginTransactionAsync();
        }

        /// <summary>
        /// 提交分布式事务
        /// </summary>
        public async Task CommitDistributedTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_distributedTransactionManager == null)
                throw new InvalidOperationException("Distributed transaction has not been started");
                
            await _distributedTransactionManager.CommitAsync();
        }

        /// <summary>
        /// 回滚分布式事务
        /// </summary>
        public async Task RollbackDistributedTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_distributedTransactionManager == null)
                return;
                
            await _distributedTransactionManager.RollbackAsync();
        }

        /// <summary>
        /// 执行分布式事务
        /// </summary>
        public async Task<T> ExecuteDistributedTransactionAsync<T>(Func<Task<T>> operation, params string[] databaseKeys)
        {
            using var transactionManager = databaseKeys.Length > 0 
                ? _dbContextFactory.CreateDistributedTransaction(databaseKeys)
                : _dbContextFactory.CreateDistributedTransaction();
                
            return await transactionManager.ExecuteAsync(operation);
        }

        /// <summary>
        /// 执行分布式事务（无返回值）
        /// </summary>
        public async Task ExecuteDistributedTransactionAsync(Func<Task> operation, params string[] databaseKeys)
        {
            using var transactionManager = databaseKeys.Length > 0 
                ? _dbContextFactory.CreateDistributedTransaction(databaseKeys)
                : _dbContextFactory.CreateDistributedTransaction();
                
            await transactionManager.ExecuteAsync(operation);
        }

        /// <summary>
        /// 释放资源的内部实现
        /// </summary>
        protected override void DisposeInternal()
        {
            _distributedTransactionManager?.Dispose();
            _contexts.Clear();
            base.DisposeInternal();
        }
    }
}
