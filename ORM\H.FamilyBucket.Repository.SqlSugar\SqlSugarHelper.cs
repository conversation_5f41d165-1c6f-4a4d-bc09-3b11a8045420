using SqlSugar;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar帮助类，支持SqlSugarClient创建和分布式事务处理
    /// </summary>
    public static class SqlSugarHelper
    {
        /// <summary>
        /// 创建SqlSugarClient
        /// </summary>
        public static SqlSugarClient CreateClient(string connectionString, DbType dbType = DbType.MySql)
        {
            return new SqlSugarClient(new ConnectionConfig
            {
                ConnectionString = connectionString,
                DbType = dbType,
                IsAutoCloseConnection = true
            });
        }

        /// <summary>
        /// 创建SqlSugarClient（使用配置）
        /// </summary>
        public static SqlSugarClient CreateClient(SqlSugarDatabaseConfig config)
        {
            return new SqlSugarClient(new ConnectionConfig
            {
                ConnectionString = config.ConnectionString,
                DbType = config.DbType,
                IsAutoCloseConnection = config.IsAutoCloseConnection
            });
        }

        /// <summary>
        /// 分布式事务执行（简单版本）
        /// </summary>
        public static async Task<bool> UseTranAsync(Func<Task> action, params SqlSugarClient[] clients)
        {
            var result = true;
            try
            {
                // 开启所有事务
                foreach (var client in clients)
                {
                    await client.Ado.BeginTranAsync();
                }
                // 执行业务
                await action();
                // 提交所有事务
                foreach (var client in clients)
                {
                    await client.Ado.CommitTranAsync();
                }
            }
            catch
            {
                result = false;
                // 回滚所有事务
                foreach (var client in clients)
                {
                    await client.Ado.RollbackTranAsync();
                }
                throw;
            }
            return result;
        }

        /// <summary>
        /// 分布式事务执行（带返回值）
        /// </summary>
        public static async Task<T> UseTranAsync<T>(Func<Task<T>> action, params SqlSugarClient[] clients)
        {
            try
            {
                // 开启所有事务
                foreach (var client in clients)
                {
                    await client.Ado.BeginTranAsync();
                }
                // 执行业务
                var result = await action();
                // 提交所有事务
                foreach (var client in clients)
                {
                    await client.Ado.CommitTranAsync();
                }
                return result;
            }
            catch
            {
                // 回滚所有事务
                foreach (var client in clients)
                {
                    await client.Ado.RollbackTranAsync();
                }
                throw;
            }
        }

        /// <summary>
        /// 使用分布式事务管理器执行事务
        /// </summary>
        public static async Task UseDistributedTranAsync(Func<Task> action, IEnumerable<SqlSugarClient> clients)
        {
            using var transactionManager = new DistributedTransactionManager();
            transactionManager.AddClients(clients);
            await transactionManager.ExecuteAsync(action);
        }

        /// <summary>
        /// 使用分布式事务管理器执行事务（带返回值）
        /// </summary>
        public static async Task<T> UseDistributedTranAsync<T>(Func<Task<T>> action, IEnumerable<SqlSugarClient> clients)
        {
            using var transactionManager = new DistributedTransactionManager();
            transactionManager.AddClients(clients);
            return await transactionManager.ExecuteAsync(action);
        }
    }
}
