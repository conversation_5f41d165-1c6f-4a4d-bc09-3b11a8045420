using System;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar.SqlServer
{
    /// <summary>
    /// SQL Server依赖注入扩展
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加SQL Server SqlSugar支持
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="databaseKey">数据库键（可选）</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSqlServerSqlSugar(
            this IServiceCollection services, 
            string connectionString, 
            string databaseKey = "default")
        {
            var config = new SqlServerDatabaseConfig(databaseKey, connectionString);
            return services.AddSqlServerSqlSugar(config);
        }

        /// <summary>
        /// 添加SQL Server SqlSugar支持（使用配置）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="config">SQL Server配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSqlServerSqlSugar(
            this IServiceCollection services, 
            SqlServerDatabaseConfig config)
        {
            ArgumentNullException.ThrowIfNull(config);

            // 添加基础SqlSugar支持
            services.AddSqlSugar(options =>
            {
                options.AddDatabase(config);
                options.SetDefaultDatabase(config.Key);
            });

            // 注册SQL Server特定的配置
            services.AddSingleton(config);

            // 注册SQL Server特定的仓储和工作单元
            services.AddScoped<SqlServerUnitOfWork>();
            services.AddScoped(typeof(SqlServerRepository<,>));

            // 替换默认的工作单元为SQL Server版本
            services.AddScoped<IUnitOfWork>(provider => provider.GetRequiredService<SqlServerUnitOfWork>());

            return services;
        }

        /// <summary>
        /// 添加SQL Server SqlSugar支持（从配置文件）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="configurationSection">配置节名称（默认为"SqlServer"）</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSqlServerSqlSugar(
            this IServiceCollection services, 
            IConfiguration configuration, 
            string configurationSection = "SqlServer")
        {
            var config = SqlServerDatabaseConfig.Create("default",
                configuration[$"{configurationSection}:Server"] ?? "localhost",
                configuration[$"{configurationSection}:Database"] ?? "DefaultDb",
                configuration[$"{configurationSection}:UserId"],
                configuration[$"{configurationSection}:Password"],
                bool.Parse(configuration[$"{configurationSection}:IntegratedSecurity"] ?? "false"));

            // 应用其他配置
            if (int.TryParse(configuration[$"{configurationSection}:CommandTimeout"], out var commandTimeout))
                config.CommandTimeout = commandTimeout;

            if (int.TryParse(configuration[$"{configurationSection}:ConnectionTimeout"], out var connectionTimeout))
                config.ConnectionTimeout = connectionTimeout;

            if (bool.TryParse(configuration[$"{configurationSection}:EnableEncryption"], out var enableEncryption))
                config.EnableEncryption = enableEncryption;

            if (bool.TryParse(configuration[$"{configurationSection}:TrustServerCertificate"], out var trustServerCertificate))
                config.TrustServerCertificate = trustServerCertificate;

            config.ApplicationName = configuration[$"{configurationSection}:ApplicationName"];

            return services.AddSqlServerSqlSugar(config);
        }

        /// <summary>
        /// 添加多个SQL Server数据库支持
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configure">配置委托</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddMultipleSqlServerSqlSugar(
            this IServiceCollection services, 
            Action<SqlServerSqlSugarOptions> configure)
        {
            var options = new SqlServerSqlSugarOptions();
            configure(options);

            // 添加基础SqlSugar支持
            services.AddSqlSugar(sqlSugarOptions =>
            {
                foreach (var config in options.Databases)
                {
                    sqlSugarOptions.AddDatabase(config);
                }
                
                if (!string.IsNullOrEmpty(options.DefaultDatabase))
                {
                    sqlSugarOptions.SetDefaultDatabase(options.DefaultDatabase);
                }
            });

            // 注册所有SQL Server配置
            foreach (var config in options.Databases)
            {
                services.AddSingleton(config);
            }

            // 注册SQL Server特定的服务
            services.AddScoped<SqlServerUnitOfWork>();
            services.AddScoped(typeof(SqlServerRepository<,>));

            return services;
        }

        /// <summary>
        /// 快速配置SQL Server（开发环境）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="server">服务器地址</param>
        /// <param name="database">数据库名</param>
        /// <param name="userId">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSqlServerSqlSugarForDevelopment(
            this IServiceCollection services,
            string server = "localhost",
            string database = "TestDb",
            string? userId = null,
            string? password = null)
        {
            var config = SqlServerDatabaseConfig.Create("default", server, database, userId, password, string.IsNullOrEmpty(userId));
            config.EnableLogging = true;
            config.EnableSensitiveDataLogging = true;
            config.TrustServerCertificate = true;
            
            return services.AddSqlServerSqlSugar(config);
        }
    }

    /// <summary>
    /// SQL Server SqlSugar配置选项
    /// </summary>
    public class SqlServerSqlSugarOptions
    {
        /// <summary>
        /// 数据库配置列表
        /// </summary>
        public List<SqlServerDatabaseConfig> Databases { get; } = [];

        /// <summary>
        /// 默认数据库键
        /// </summary>
        public string? DefaultDatabase { get; set; }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        /// <param name="config">配置</param>
        /// <returns>配置选项</returns>
        public SqlServerSqlSugarOptions AddDatabase(SqlServerDatabaseConfig config)
        {
            Databases.Add(config);
            return this;
        }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        /// <param name="key">数据库键</param>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>配置选项</returns>
        public SqlServerSqlSugarOptions AddDatabase(string key, string connectionString)
        {
            return AddDatabase(new SqlServerDatabaseConfig(key, connectionString));
        }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        /// <param name="key">数据库键</param>
        /// <param name="server">服务器</param>
        /// <param name="database">数据库名</param>
        /// <param name="userId">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="integratedSecurity">集成安全验证</param>
        /// <returns>配置选项</returns>
        public SqlServerSqlSugarOptions AddDatabase(
            string key, 
            string server, 
            string database, 
            string? userId = null, 
            string? password = null, 
            bool integratedSecurity = false)
        {
            return AddDatabase(SqlServerDatabaseConfig.Create(key, server, database, userId, password, integratedSecurity));
        }

        /// <summary>
        /// 设置默认数据库
        /// </summary>
        /// <param name="databaseKey">数据库键</param>
        /// <returns>配置选项</returns>
        public SqlServerSqlSugarOptions SetDefaultDatabase(string databaseKey)
        {
            DefaultDatabase = databaseKey;
            return this;
        }
    }
}
