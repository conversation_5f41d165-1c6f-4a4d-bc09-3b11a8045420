﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>H.FamilyBucket.Repository.SqlSugar.SqlServer</PackageId>
    <Version>1.0.0</Version>
    <Authors>H.FamilyBucket</Authors>
    <Description>SqlSugar SQL Server implementation for H.FamilyBucket Repository pattern</Description>
    <PackageTags>SqlSugar;SqlServer;Repository;ORM;DDD</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\H.FamilyBucket.Repository.SqlSugar\H.FamilyBucket.Repository.SqlSugar.csproj" />
  </ItemGroup>

</Project>
