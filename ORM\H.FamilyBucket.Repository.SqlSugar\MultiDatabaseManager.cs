using SqlSugar;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// 多数据库管理器
    /// </summary>
    public class MultiDatabaseManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, SqlSugarClient> _clients = new();
        private readonly ConcurrentDictionary<string, SqlSugarDatabaseConfig> _configs = new();
        private string _defaultKey = "default";
        private bool _disposed = false;

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        public void AddDatabase(SqlSugarDatabaseConfig config)
        {
            if (string.IsNullOrEmpty(config.Key))
                throw new ArgumentException("Database key cannot be null or empty");

            _configs.AddOrUpdate(config.Key, config, (key, oldValue) => config);

            // 如果是第一个配置，设为默认
            if (_configs.Count == 1)
                _defaultKey = config.Key;
        }

        /// <summary>
        /// 批量添加数据库配置
        /// </summary>
        public void AddDatabases(IEnumerable<SqlSugarDatabaseConfig> configs)
        {
            foreach (var config in configs)
            {
                AddDatabase(config);
            }
        }

        /// <summary>
        /// 设置默认数据库
        /// </summary>
        public void SetDefaultDatabase(string key)
        {
            if (!_configs.ContainsKey(key))
                throw new ArgumentException($"Database with key '{key}' not found");
            
            _defaultKey = key;
        }

        /// <summary>
        /// 获取数据库客户端
        /// </summary>
        public SqlSugarClient GetClient(string? key = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(MultiDatabaseManager));
                
            key ??= _defaultKey;
            
            return _clients.GetOrAdd(key, k =>
            {
                if (!_configs.TryGetValue(k, out var config))
                    throw new ArgumentException($"Database with key '{k}' not found");
                
                return CreateClient(config);
            });
        }

        /// <summary>
        /// 获取所有客户端
        /// </summary>
        public IEnumerable<SqlSugarClient> GetAllClients()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(MultiDatabaseManager));
                
            foreach (var key in _configs.Keys)
            {
                yield return GetClient(key);
            }
        }

        /// <summary>
        /// 获取指定数据库的客户端列表
        /// </summary>
        public IEnumerable<SqlSugarClient> GetClients(params string[] keys)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(MultiDatabaseManager));
                
            foreach (var key in keys)
            {
                yield return GetClient(key);
            }
        }

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        public bool DatabaseExists(string key)
        {
            return _configs.ContainsKey(key);
        }

        /// <summary>
        /// 获取所有数据库键
        /// </summary>
        public IEnumerable<string> GetDatabaseKeys()
        {
            return _configs.Keys.ToList();
        }

        /// <summary>
        /// 移除数据库配置
        /// </summary>
        public bool RemoveDatabase(string key)
        {
            if (_configs.TryRemove(key, out _))
            {
                if (_clients.TryRemove(key, out var client))
                {
                    client?.Dispose();
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                foreach (var client in _clients.Values)
                {
                    client?.Dispose();
                }
                _clients.Clear();
                _configs.Clear();
                _disposed = true;
            }
        }

        private SqlSugarClient CreateClient(SqlSugarDatabaseConfig config)
        {
            var connectionConfig = new ConnectionConfig
            {
                ConnectionString = config.ConnectionString,
                DbType = config.DbType,
                IsAutoCloseConnection = config.IsAutoCloseConnection
            };

            // 如果启用读写分离
            if (config.IsReadWriteSeparation && config.SlaveConnectionStrings.Count > 0)
            {
                var slaveConfigs = config.SlaveConnectionStrings.Select(cs => new SlaveConnectionConfig
                {
                    ConnectionString = cs,
                    HitRate = 10 // 命中率
                }).ToList();

                connectionConfig.SlaveConnectionConfigs = slaveConfigs;
            }

            var client = new SqlSugarClient(connectionConfig);
            
            if (config.IsLogSql)
            {
                client.Aop.OnLogExecuting = (sql, pars) =>
                {
                    Console.WriteLine($"[{config.Key}] SQL: {sql}");
                    if (pars != null && pars.Length > 0)
                    {
                        Console.WriteLine($"[{config.Key}] Parameters: {string.Join(", ", pars.AsEnumerable())}");
                    }
                };
            }

            return client;
        }
    }
}
