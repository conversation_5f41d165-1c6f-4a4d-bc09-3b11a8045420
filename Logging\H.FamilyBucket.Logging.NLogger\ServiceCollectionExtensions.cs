﻿using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;

namespace H.FamilyBucket.Logging.NLogger
{
    /// <summary>
    /// 依赖注入扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加NLog日志服务（使用默认配置）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddNLogger(this IServiceCollection services)
        {
            return AddNLogger(services, new LoggerConfiguration());
        }

        /// <summary>
        /// 添加NLog日志服务（使用指定配置文件）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddNLogger(this IServiceCollection services, string configFilePath)
        {
            var configuration = new LoggerConfiguration
            {
                ConfigFilePath = configFilePath
            };
            return AddNLogger(services, configuration);
        }

        /// <summary>
        /// 添加NLog日志服务（使用指定配置）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">日志配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddNLogger(this IServiceCollection services, LoggerConfiguration configuration)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));

            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // 注册日志配置
            services.TryAddSingleton(configuration);

            // 注册日志服务
            services.TryAddSingleton<ILoggerService>(provider => 
                new NLoggerService("Application", configuration));

            // 添加Microsoft.Extensions.Logging支持
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);
                builder.AddNLog(configuration.ConfigFilePath);
            });

            return services;
        }

        /// <summary>
        /// 添加NLog日志服务（使用配置委托）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configureOptions">配置委托</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddNLogger(this IServiceCollection services, Action<LoggerConfiguration> configureOptions)
        {
            if (configureOptions == null)
                throw new ArgumentNullException(nameof(configureOptions));

            var configuration = new LoggerConfiguration();
            configureOptions(configuration);

            return AddNLogger(services, configuration);
        }

        /// <summary>
        /// 添加作用域日志服务工厂
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddScopedLoggerFactory(this IServiceCollection services)
        {
            services.TryAddScoped<ILoggerFactory>(provider =>
            {
                var configuration = provider.GetService<LoggerConfiguration>() ?? new LoggerConfiguration();
                return new LoggerFactory(categoryName => new NLoggerService(categoryName, configuration));
            });

            return services;
        }
    }

    /// <summary>
    /// 日志工厂接口
    /// </summary>
    public interface ILoggerFactory
    {
        /// <summary>
        /// 创建日志服务
        /// </summary>
        /// <param name="categoryName">类别名称</param>
        /// <returns>日志服务实例</returns>
        ILoggerService CreateLogger(string categoryName);

        /// <summary>
        /// 创建日志服务
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>日志服务实例</returns>
        ILoggerService CreateLogger<T>();
    }

    /// <summary>
    /// 日志工厂实现
    /// </summary>
    public class LoggerFactory : ILoggerFactory
    {
        private readonly Func<string, ILoggerService> _loggerFactory;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggerFactory">日志工厂委托</param>
        public LoggerFactory(Func<string, ILoggerService> loggerFactory)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        }

        /// <summary>
        /// 创建日志服务
        /// </summary>
        /// <param name="categoryName">类别名称</param>
        /// <returns>日志服务实例</returns>
        public ILoggerService CreateLogger(string categoryName)
        {
            return _loggerFactory(categoryName);
        }

        /// <summary>
        /// 创建日志服务
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>日志服务实例</returns>
        public ILoggerService CreateLogger<T>()
        {
            return _loggerFactory(typeof(T).FullName);
        }
    }
}
