using System;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 审计实体接口
    /// </summary>
    public interface IAuditableEntity
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        DateTime CreatedAt { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        string? CreatedBy { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 最后修改者
        /// </summary>
        string? UpdatedBy { get; set; }
    }
}
