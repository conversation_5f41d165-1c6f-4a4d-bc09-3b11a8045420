using System.Collections.Generic;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 分页结果接口
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public interface IPagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        IEnumerable<T> Data { get; }

        /// <summary>
        /// 总记录数
        /// </summary>
        int TotalCount { get; }

        /// <summary>
        /// 当前页码
        /// </summary>
        int PageIndex { get; }

        /// <summary>
        /// 每页大小
        /// </summary>
        int PageSize { get; }

        /// <summary>
        /// 总页数
        /// </summary>
        int TotalPages { get; }

        /// <summary>
        /// 是否有上一页
        /// </summary>
        bool HasPreviousPage { get; }

        /// <summary>
        /// 是否有下一页
        /// </summary>
        bool HasNextPage { get; }
    }
}
