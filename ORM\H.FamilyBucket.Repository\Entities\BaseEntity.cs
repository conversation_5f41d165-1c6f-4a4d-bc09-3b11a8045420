namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 基础实体类
    /// </summary>
    /// <typeparam name="TKey">主键类型</typeparam>
    public abstract class BaseEntity<TKey> : IEntity<TKey>
    {
        /// <summary>
        /// 主键
        /// </summary>
        public virtual TKey Id { get; set; } = default!;

        /// <summary>
        /// 重写相等比较
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is not BaseEntity<TKey> other)
                return false;

            if (ReferenceEquals(this, other))
                return true;

            if (GetType() != other.GetType())
                return false;

            if (Id == null || other.Id == null)
                return false;

            return Id.Equals(other.Id);
        }

        /// <summary>
        /// 重写哈希码
        /// </summary>
        public override int GetHashCode()
        {
            return (GetType().ToString() + Id).GetHashCode();
        }

        /// <summary>
        /// 相等操作符
        /// </summary>
        public static bool operator ==(BaseEntity<TKey>? a, BaseEntity<TKey>? b)
        {
            if (a is null && b is null)
                return true;

            if (a is null || b is null)
                return false;

            return a.Equals(b);
        }

        /// <summary>
        /// 不等操作符
        /// </summary>
        public static bool operator !=(BaseEntity<TKey>? a, BaseEntity<TKey>? b)
        {
            return !(a == b);
        }
    }

    /// <summary>
    /// 基础实体类（long主键）
    /// </summary>
    public abstract class BaseEntity : BaseEntity<long>, IEntity
    {
    }
}
