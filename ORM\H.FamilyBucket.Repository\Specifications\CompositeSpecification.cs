using System.Collections.Generic;
using System.Linq;

namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// 复合规约
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public class CompositeSpecification<T> : BaseSpecification<T>
    {
        private readonly List<ISpecification<T>> _specifications = new();

        public CompositeSpecification(params ISpecification<T>[] specifications)
        {
            _specifications.AddRange(specifications);
        }

        /// <summary>
        /// 添加规约
        /// </summary>
        public void Add(ISpecification<T> specification)
        {
            _specifications.Add(specification);
        }

        /// <summary>
        /// 获取所有规约
        /// </summary>
        public IReadOnlyList<ISpecification<T>> Specifications => _specifications.AsReadOnly();
    }
}
