using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.Context;
using H.FamilyBucket.Repository.IRepositories;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar数据库上下文实现
    /// </summary>
    public class SqlSugarDbContext : BaseDbContext
    {
        private readonly SqlSugarClient _client;
        private readonly SqlSugarDatabaseConfig _sqlSugarConfig;

        public SqlSugarDbContext(SqlSugarDatabaseConfig configuration) : base(configuration)
        {
            _sqlSugarConfig = configuration;
            _client = CreateSqlSugarClient(configuration);
        }

        public SqlSugarDbContext(SqlSugarClient client, SqlSugarDatabaseConfig configuration) : base(configuration)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _sqlSugarConfig = configuration;
        }

        /// <summary>
        /// 获取SqlSugar客户端
        /// </summary>
        public SqlSugarClient Client => _client;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public override bool IsConnected => _client?.Ado?.Connection?.State == System.Data.ConnectionState.Open;

        /// <summary>
        /// 保存更改
        /// </summary>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // SqlSugar不需要显式保存更改，操作会立即执行
            return await Task.FromResult(0);
        }

        /// <summary>
        /// 执行SQL命令
        /// </summary>
        public override async Task<int> ExecuteAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await _client.Ado.ExecuteCommandAsync(sql, parameters);
        }

        /// <summary>
        /// 查询数据
        /// </summary>
        public override async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await _client.Ado.SqlQueryAsync<T>(sql, parameters);
        }

        /// <summary>
        /// 查询单个值
        /// </summary>
        public override async Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            var result = await _client.Ado.GetScalarAsync(sql, parameters);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        /// <summary>
        /// 重新加载实体
        /// </summary>
        public override async Task ReloadAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default)
        {
            // SqlSugar中重新加载实体的逻辑
            await Task.CompletedTask;
        }

        /// <summary>
        /// 开始事务的内部实现
        /// </summary>
        protected override async Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken)
        {
            await _client.Ado.BeginTranAsync();
            return new SqlSugarDbTransaction(_client);
        }

        /// <summary>
        /// 创建数据库集合
        /// </summary>
        protected override IDbSet<TEntity> CreateDbSet<TEntity>()
        {
            // 使用反射创建 SqlSugarDbSet 实例，因为基类约束不包含 new()
            return (IDbSet<TEntity>)Activator.CreateInstance(
                typeof(SqlSugarDbSet<>).MakeGenericType(typeof(TEntity)),
                _client)!;
        }

        /// <summary>
        /// 释放资源的内部实现
        /// </summary>
        protected override void DisposeInternal()
        {
            _client?.Dispose();
        }

        private SqlSugarClient CreateSqlSugarClient(SqlSugarDatabaseConfig config)
        {
            var connectionConfig = new ConnectionConfig
            {
                ConnectionString = config.ConnectionString,
                DbType = config.DbType,
                IsAutoCloseConnection = config.IsAutoCloseConnection
            };

            // 如果启用读写分离
            if (config.IsReadWriteSeparation && config.SlaveConnectionStrings.Count > 0)
            {
                var slaveConfigs = config.SlaveConnectionStrings.Select(cs => new SlaveConnectionConfig
                {
                    ConnectionString = cs,
                    HitRate = 10 // 命中率
                }).ToList();

                connectionConfig.SlaveConnectionConfigs = slaveConfigs;
            }

            var client = new SqlSugarClient(connectionConfig);

            if (config.IsLogSql || config.EnableLogging)
            {
                client.Aop.OnLogExecuting = (sql, pars) =>
                {
                    Console.WriteLine($"[{config.Key}] SQL: {sql}");
                    if (pars != null && pars.Length > 0)
                    {
                        Console.WriteLine($"[{config.Key}] Parameters: {string.Join(", ", pars.AsEnumerable())}");
                    }
                };
            }

            return client;
        }
    }
}
