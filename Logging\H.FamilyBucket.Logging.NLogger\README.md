﻿# H.FamilyBucket.Logging.NLogger

通用的NLog日志工具类DLL，支持依赖注入和直接使用，可以读取指定的nlog.config配置文件。

## 功能特性

- ✅ 支持依赖注入
- ✅ 支持直接静态调用
- ✅ 支持自定义配置文件路径
- ✅ 支持程序化配置
- ✅ 支持多种日志级别（Trace, Debug, Info, Warn, Error, Fatal）
- ✅ 支持异常日志记录
- ✅ 自动创建日志目录
- ✅ 支持日志文件归档和压缩
- ✅ 兼容Microsoft.Extensions.Logging

## 安装

将项目编译后的DLL引用到您的项目中，或者直接引用项目。

## 使用方式

### 1. 直接使用（静态方式）

```csharp
using H.FamilyBucket.Logging.NLogger;

// 使用默认配置
LogHelper.Info("这是一条信息日志");
LogHelper.Error("这是一条错误日志");

// 使用异常信息
try
{
    // 一些可能出错的代码
}
catch (Exception ex)
{
    LogHelper.Error(ex, "操作失败：{0}", "具体操作");
}

// 使用指定配置文件初始化
LogHelper.Initialize("custom-nlog.config");
LogHelper.Info("使用自定义配置的日志");

// 使用配置对象初始化
var config = new LoggerConfiguration
{
    ConfigFilePath = "logs/nlog.config",
    LogDirectory = "logs",
    EnableConsole = true,
    ConsoleMinLevel = LogLevel.Info
};
LogHelper.Initialize(config);
```

### 2. 依赖注入方式

#### 在Startup.cs或Program.cs中注册服务：

```csharp
using H.FamilyBucket.Logging.NLogger;

// 方式1：使用默认配置
services.AddNLogger();

// 方式2：使用指定配置文件
services.AddNLogger("custom-nlog.config");

// 方式3：使用配置对象
var loggerConfig = new LoggerConfiguration
{
    ConfigFilePath = "nlog.config",
    LogDirectory = "logs",
    EnableConsole = true,
    EnableFile = true,
    ConsoleMinLevel = LogLevel.Info
};
services.AddNLogger(loggerConfig);

// 方式4：使用配置委托
services.AddNLogger(config =>
{
    config.ConfigFilePath = "nlog.config";
    config.LogDirectory = "logs";
    config.EnableConsole = true;
    config.ConsoleMinLevel = LogLevel.Debug;
});

// 可选：添加作用域日志工厂
services.AddScopedLoggerFactory();
```

#### 在控制器或服务中使用：

```csharp
public class HomeController : Controller
{
    private readonly ILoggerService _logger;

    public HomeController(ILoggerService logger)
    {
        _logger = logger;
    }

    public IActionResult Index()
    {
        _logger.Info("访问首页");
        
        try
        {
            // 业务逻辑
            return View();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "首页访问出错");
            return View("Error");
        }
    }
}
```

#### 使用日志工厂：

```csharp
public class SomeService
{
    private readonly ILoggerService _logger;

    public SomeService(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger<SomeService>();
        // 或者
        // _logger = loggerFactory.CreateLogger("SomeService");
    }

    public void DoSomething()
    {
        _logger.Debug("开始执行某项操作");
        // 业务逻辑
        _logger.Info("操作完成");
    }
}
```

### 3. 创建特定类别的日志器

```csharp
// 为特定类创建日志器
var logger = LogHelper.CreateLogger<MyClass>();
logger.Info("这是MyClass的日志");

// 为特定名称创建日志器
var namedLogger = LogHelper.CreateLogger("MyModule");
namedLogger.Warn("这是MyModule的警告日志");
```

## 配置说明

### LoggerConfiguration 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ConfigFilePath | string | "nlog.config" | NLog配置文件路径 |
| AutoReload | bool | true | 是否自动重载配置文件 |
| InternalLogLevel | string | "Info" | 内部日志级别 |
| InternalLogFile | string | "logs/internal-nlog.txt" | 内部日志文件路径 |
| LogDirectory | string | "logs" | 日志目录 |
| EnableConsole | bool | true | 是否启用控制台输出 |
| ConsoleMinLevel | LogLevel | Info | 控制台输出的最小日志级别 |
| EnableFile | bool | true | 是否启用文件输出 |
| ArchiveAboveSize | long | 10485760 | 文件归档大小限制（10MB） |
| MaxArchiveFiles | int | 20 | 最大归档文件数量 |
| EnableArchiveFileCompression | bool | false | 是否启用归档文件压缩 |

### 日志级别

- **Trace**: 最详细的日志信息，通常只在开发时使用
- **Debug**: 调试信息，用于开发和测试
- **Info**: 一般信息，记录程序的正常运行
- **Warn**: 警告信息，表示可能的问题
- **Error**: 错误信息，记录错误但程序可以继续运行
- **Fatal**: 致命错误，可能导致程序终止

## 配置文件示例

项目中包含了一个完整的 `nlog.config` 配置文件示例，支持：

- 控制台输出
- 按日期分类的日志文件
- 错误日志单独存储
- 自动归档和文件大小限制
- 过滤Microsoft框架日志

## 注意事项

1. 确保应用程序对日志目录有写入权限
2. 在生产环境中建议将控制台日志级别设置为Info或更高
3. 定期清理旧的日志文件以节省磁盘空间
4. 配置文件路径支持相对路径和绝对路径

## 兼容性

- .NET Standard 2.1
- 支持 .NET Core 3.1+
- 支持 .NET 5+
- 支持 .NET Framework 4.7.2+
