using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.Context;
using System;

namespace H.FamilyBucket.Repository
{
    /// <summary>
    /// Extension methods for IServiceCollection to register repositories
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加基础仓储支持
        /// </summary>
        public static IServiceCollection AddRepository(this IServiceCollection services)
        {
            return services;
        }

        /// <summary>
        /// 添加仓储实现（指定接口和实现类型）
        /// </summary>
        public static IServiceCollection AddRepository<TInterface, TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TInterface : class
            where TImplementation : class, TInterface
        {
            services.Add(new ServiceDescriptor(typeof(TInterface), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加仓储实现（指定实体和主键类型）
        /// </summary>
        public static IServiceCollection AddRepository<TEntity, TKey, TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IRepository<TEntity, TKey>
        {
            services.Add(new ServiceDescriptor(typeof(IRepository<TEntity, TKey>), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加只读仓储实现
        /// </summary>
        public static IServiceCollection AddReadOnlyRepository<TEntity, TKey, TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IReadOnlyRepository<TEntity, TKey>
        {
            services.Add(new ServiceDescriptor(typeof(IReadOnlyRepository<TEntity, TKey>), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加高级仓储实现
        /// </summary>
        public static IServiceCollection AddAdvancedRepository<TEntity, TKey, TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IAdvancedRepository<TEntity, TKey>
        {
            services.Add(new ServiceDescriptor(typeof(IAdvancedRepository<TEntity, TKey>), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加工作单元
        /// </summary>
        public static IServiceCollection AddUnitOfWork<TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TImplementation : class, IUnitOfWork
        {
            services.Add(new ServiceDescriptor(typeof(IUnitOfWork), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加分布式工作单元
        /// </summary>
        public static IServiceCollection AddDistributedUnitOfWork<TImplementation>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TImplementation : class, IDistributedUnitOfWork
        {
            services.Add(new ServiceDescriptor(typeof(IDistributedUnitOfWork), typeof(TImplementation), lifetime));
            services.Add(new ServiceDescriptor(typeof(IUnitOfWork), typeof(TImplementation), lifetime));
            return services;
        }

        /// <summary>
        /// 添加数据库上下文
        /// </summary>
        public static IServiceCollection AddDbContext<TContext>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TContext : class, IDbContext
        {
            services.Add(new ServiceDescriptor(typeof(IDbContext), typeof(TContext), lifetime));
            services.Add(new ServiceDescriptor(typeof(TContext), typeof(TContext), lifetime));
            return services;
        }

        /// <summary>
        /// 添加数据库上下文工厂
        /// </summary>
        public static IServiceCollection AddDbContextFactory<TFactory>(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Singleton)
            where TFactory : class, IDbContextFactory
        {
            services.Add(new ServiceDescriptor(typeof(IDbContextFactory), typeof(TFactory), lifetime));
            return services;
        }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        public static IServiceCollection AddDbConfiguration(this IServiceCollection services, IDbConfiguration configuration)
        {
            services.AddSingleton(configuration);
            return services;
        }

        /// <summary>
        /// 添加数据库配置
        /// </summary>
        public static IServiceCollection AddDbConfiguration(this IServiceCollection services, string key, string connectionString, string databaseType)
        {
            var configuration = new DbConfiguration(key, connectionString, databaseType);
            return services.AddDbConfiguration(configuration);
        }

        /// <summary>
        /// 批量注册仓储
        /// </summary>
        public static IServiceCollection AddRepositories(this IServiceCollection services, Action<RepositoryRegistrationBuilder> configure)
        {
            var builder = new RepositoryRegistrationBuilder(services);
            configure(builder);
            return services;
        }
    }
}