using System;
using System.Collections.Generic;

namespace H.FamilyBucket.Repository.Context
{
    /// <summary>
    /// 数据库配置实现
    /// </summary>
    public class DbConfiguration : IDbConfiguration
    {
        public DbConfiguration(string key, string connectionString, string databaseType)
        {
            Key = key ?? throw new ArgumentNullException(nameof(key));
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            DatabaseType = databaseType ?? throw new ArgumentNullException(nameof(databaseType));
            Properties = new Dictionary<string, object>();
        }

        public string Key { get; }
        public string ConnectionString { get; }
        public string DatabaseType { get; }
        public int CommandTimeout { get; set; } = 30;
        public bool EnableLogging { get; set; } = false;
        public bool EnableSensitiveDataLogging { get; set; } = false;
        public int MaxRetryCount { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
        public int PoolSize { get; set; } = 100;
        public bool EnableConnectionPooling { get; set; } = true;
        public Dictionary<string, object> Properties { get; }
    }
}
