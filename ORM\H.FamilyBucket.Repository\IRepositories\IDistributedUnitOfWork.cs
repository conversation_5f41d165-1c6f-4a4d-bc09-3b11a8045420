using System;
using System.Threading.Tasks;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 分布式工作单元接口
    /// </summary>
    public interface IDistributedUnitOfWork : IUnitOfWork
    {
        /// <summary>
        /// 添加数据库上下文
        /// </summary>
        void AddContext(string key, object context);

        /// <summary>
        /// 获取数据库上下文
        /// </summary>
        T GetContext<T>(string key) where T : class;

        /// <summary>
        /// 获取指定数据库的仓储
        /// </summary>
        IRepository<TEntity, TKey> GetRepository<TEntity, TKey>(string databaseKey) where TEntity : class;

        /// <summary>
        /// 获取指定数据库的只读仓储
        /// </summary>
        IReadOnlyRepository<TEntity, TKey> GetReadOnlyRepository<TEntity, TKey>(string databaseKey) where TEntity : class;

        /// <summary>
        /// 获取指定数据库的高级仓储
        /// </summary>
        IAdvancedRepository<TEntity, TKey> GetAdvancedRepository<TEntity, TKey>(string databaseKey) where TEntity : class;

        /// <summary>
        /// 开始分布式事务
        /// </summary>
        Task BeginDistributedTransactionAsync(params string[] databaseKeys);

        /// <summary>
        /// 提交分布式事务
        /// </summary>
        Task CommitDistributedTransactionAsync(System.Threading.CancellationToken cancellationToken = default);

        /// <summary>
        /// 回滚分布式事务
        /// </summary>
        Task RollbackDistributedTransactionAsync(System.Threading.CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行分布式事务
        /// </summary>
        Task<T> ExecuteDistributedTransactionAsync<T>(Func<Task<T>> operation, params string[] databaseKeys);

        /// <summary>
        /// 执行分布式事务（无返回值）
        /// </summary>
        Task ExecuteDistributedTransactionAsync(Func<Task> operation, params string[] databaseKeys);
    }
}
