namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 完整的实体接口，包含所有功能
    /// </summary>
    /// <typeparam name="TKey">主键类型</typeparam>
    public interface IFullEntity<TKey> : IEntity<TKey>, IAuditableEntity, ISoftDeleteEntity, IVersionedEntity
    {
    }

    /// <summary>
    /// 完整的实体接口（long主键）
    /// </summary>
    public interface IFullEntity : IFullEntity<long>
    {
    }
}
