using SqlSugar;
using System.Collections.Generic;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar数据库上下文工厂接口
    /// </summary>
    public interface ISqlSugarDbContextFactory
    {
        /// <summary>
        /// 获取默认数据库客户端
        /// </summary>
        SqlSugarClient GetClient();

        /// <summary>
        /// 获取指定数据库客户端
        /// </summary>
        SqlSugarClient GetClient(string key);

        /// <summary>
        /// 获取所有数据库客户端
        /// </summary>
        IEnumerable<SqlSugarClient> GetAllClients();

        /// <summary>
        /// 获取指定数据库的客户端列表
        /// </summary>
        IEnumerable<SqlSugarClient> GetClients(params string[] keys);

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        bool DatabaseExists(string key);

        /// <summary>
        /// 获取所有数据库键
        /// </summary>
        IEnumerable<string> GetDatabaseKeys();

        /// <summary>
        /// 创建分布式事务管理器
        /// </summary>
        DistributedTransactionManager CreateDistributedTransaction();

        /// <summary>
        /// 创建分布式事务管理器（指定数据库）
        /// </summary>
        DistributedTransactionManager CreateDistributedTransaction(params string[] databaseKeys);
    }
}
