using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository.Repositories
{
    /// <summary>
    /// 工作单元基础实现
    /// </summary>
    public abstract class BaseUnitOfWork : IUnitOfWork
    {
        private readonly ConcurrentDictionary<Type, object> _repositories = new();
        private bool _disposed = false;
        protected IDbTransaction? _transaction;

        /// <summary>
        /// 是否在事务中
        /// </summary>
        public bool IsInTransaction => _transaction != null;

        /// <summary>
        /// 当前事务
        /// </summary>
        public IDbTransaction? CurrentTransaction => _transaction;

        /// <summary>
        /// 获取仓储
        /// </summary>
        public virtual IRepository<TEntity, TKey> GetRepository<TEntity, TKey>() where TEntity : class
        {
            var type = typeof(IRepository<TEntity, TKey>);
            return (IRepository<TEntity, TKey>)_repositories.GetOrAdd(type, _ => CreateRepository<TEntity, TKey>());
        }

        /// <summary>
        /// 获取只读仓储
        /// </summary>
        public virtual IReadOnlyRepository<TEntity, TKey> GetReadOnlyRepository<TEntity, TKey>() where TEntity : class
        {
            var type = typeof(IReadOnlyRepository<TEntity, TKey>);
            return (IReadOnlyRepository<TEntity, TKey>)_repositories.GetOrAdd(type, _ => CreateReadOnlyRepository<TEntity, TKey>());
        }

        /// <summary>
        /// 获取高级仓储
        /// </summary>
        public virtual IAdvancedRepository<TEntity, TKey> GetAdvancedRepository<TEntity, TKey>() where TEntity : class
        {
            var type = typeof(IAdvancedRepository<TEntity, TKey>);
            return (IAdvancedRepository<TEntity, TKey>)_repositories.GetOrAdd(type, _ => CreateAdvancedRepository<TEntity, TKey>());
        }

        /// <summary>
        /// 开始事务
        /// </summary>
        public virtual async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction != null)
                throw new InvalidOperationException("Transaction has already been started");

            _transaction = await BeginTransactionInternalAsync(cancellationToken);
        }

        /// <summary>
        /// 提交事务
        /// </summary>
        public virtual async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction == null)
                throw new InvalidOperationException("No transaction has been started");

            try
            {
                await _transaction.CommitAsync(cancellationToken);
            }
            finally
            {
                _transaction?.Dispose();
                _transaction = null;
            }
        }

        /// <summary>
        /// 回滚事务
        /// </summary>
        public virtual async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction == null)
                return;

            try
            {
                await _transaction.RollbackAsync(cancellationToken);
            }
            finally
            {
                _transaction?.Dispose();
                _transaction = null;
            }
        }

        /// <summary>
        /// 保存更改
        /// </summary>
        public virtual async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await SaveChangesInternalAsync(cancellationToken);
        }

        /// <summary>
        /// 执行事务
        /// </summary>
        public virtual async Task<T> ExecuteTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));

            var wasInTransaction = IsInTransaction;
            
            if (!wasInTransaction)
                await BeginTransactionAsync(cancellationToken);

            try
            {
                var result = await operation();
                
                if (!wasInTransaction)
                    await CommitAsync(cancellationToken);
                    
                return result;
            }
            catch
            {
                if (!wasInTransaction)
                    await RollbackAsync(cancellationToken);
                throw;
            }
        }

        /// <summary>
        /// 执行事务（无返回值）
        /// </summary>
        public virtual async Task ExecuteTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
        {
            await ExecuteTransactionAsync(async () =>
            {
                await operation();
                return 0;
            }, cancellationToken);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            if (!_disposed)
            {
                _transaction?.Dispose();
                _repositories.Clear();
                DisposeInternal();
                _disposed = true;
            }
        }

        #region 抽象方法

        /// <summary>
        /// 创建仓储实例
        /// </summary>
        protected abstract IRepository<TEntity, TKey> CreateRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 创建只读仓储实例
        /// </summary>
        protected abstract IReadOnlyRepository<TEntity, TKey> CreateReadOnlyRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 创建高级仓储实例
        /// </summary>
        protected abstract IAdvancedRepository<TEntity, TKey> CreateAdvancedRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 开始事务的内部实现
        /// </summary>
        protected abstract Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken);

        /// <summary>
        /// 保存更改的内部实现
        /// </summary>
        protected abstract Task<int> SaveChangesInternalAsync(CancellationToken cancellationToken);

        /// <summary>
        /// 释放资源的内部实现
        /// </summary>
        protected virtual void DisposeInternal()
        {
            // 子类可以重写此方法来释放特定资源
        }

        #endregion
    }
}
