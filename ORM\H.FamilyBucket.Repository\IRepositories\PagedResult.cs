using System;
using System.Collections.Generic;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 分页结果实现
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResult<T> : IPagedResult<T>
    {
        public PagedResult(IEnumerable<T> data, int totalCount, int pageIndex, int pageSize)
        {
            Data = data ?? throw new ArgumentNullException(nameof(data));
            TotalCount = totalCount;
            PageIndex = pageIndex;
            PageSize = pageSize;
        }

        public IEnumerable<T> Data { get; }
        public int TotalCount { get; }
        public int PageIndex { get; }
        public int PageSize { get; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageIndex > 1;
        public bool HasNextPage => PageIndex < TotalPages;
    }
}
