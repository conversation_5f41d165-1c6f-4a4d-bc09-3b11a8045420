using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// 分布式事务管理器
    /// </summary>
    public class DistributedTransactionManager : IDisposable
    {
        private readonly List<SqlSugarClient> _clients = new();
        private readonly List<IDbTransaction> _transactions = new();
        private bool _disposed = false;
        private bool _transactionStarted = false;

        /// <summary>
        /// 添加参与事务的数据库客户端
        /// </summary>
        public void AddClient(SqlSugarClient client)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));
                
            if (_transactionStarted)
                throw new InvalidOperationException("Cannot add clients after transaction has started");
                
            _clients.Add(client);
        }

        /// <summary>
        /// 批量添加参与事务的数据库客户端
        /// </summary>
        public void AddClients(IEnumerable<SqlSugarClient> clients)
        {
            foreach (var client in clients)
            {
                AddClient(client);
            }
        }

        /// <summary>
        /// 开始分布式事务
        /// </summary>
        public async Task BeginTransactionAsync()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));
                
            if (_transactionStarted)
                throw new InvalidOperationException("Transaction has already been started");
                
            if (!_clients.Any())
                throw new InvalidOperationException("No clients added to transaction");

            try
            {
                foreach (var client in _clients)
                {
                    await client.Ado.BeginTranAsync();
                    _transactions.Add(new SqlSugarDbTransaction(client));
                }
                _transactionStarted = true;
            }
            catch
            {
                // 如果开始事务失败，回滚已开始的事务
                await RollbackInternalAsync();
                throw;
            }
        }

        /// <summary>
        /// 提交分布式事务
        /// </summary>
        public async Task CommitAsync()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));
                
            if (!_transactionStarted)
                throw new InvalidOperationException("Transaction has not been started");

            try
            {
                // 两阶段提交：先准备，再提交
                foreach (var client in _clients)
                {
                    await client.Ado.CommitTranAsync();
                }
                _transactionStarted = false;
            }
            catch
            {
                // 提交失败，尝试回滚
                await RollbackInternalAsync();
                throw;
            }
        }

        /// <summary>
        /// 回滚分布式事务
        /// </summary>
        public async Task RollbackAsync()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));
                
            if (!_transactionStarted)
                return;

            await RollbackInternalAsync();
        }

        /// <summary>
        /// 执行分布式事务
        /// </summary>
        public async Task<T> ExecuteAsync<T>(Func<Task<T>> action)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));

            await BeginTransactionAsync();
            try
            {
                var result = await action();
                await CommitAsync();
                return result;
            }
            catch
            {
                await RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 执行分布式事务（无返回值）
        /// </summary>
        public async Task ExecuteAsync(Func<Task> action)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DistributedTransactionManager));

            await BeginTransactionAsync();
            try
            {
                await action();
                await CommitAsync();
            }
            catch
            {
                await RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 获取事务状态
        /// </summary>
        public bool IsTransactionStarted => _transactionStarted;

        /// <summary>
        /// 获取参与事务的客户端数量
        /// </summary>
        public int ClientCount => _clients.Count;

        private async Task RollbackInternalAsync()
        {
            var exceptions = new List<Exception>();
            
            foreach (var client in _clients)
            {
                try
                {
                    await client.Ado.RollbackTranAsync();
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            }
            
            _transactionStarted = false;
            
            if (exceptions.Any())
            {
                throw new AggregateException("One or more errors occurred during rollback", exceptions);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                if (_transactionStarted)
                {
                    // 同步回滚
                    foreach (var client in _clients)
                    {
                        try
                        {
                            client.Ado.RollbackTran();
                        }
                        catch
                        {
                            // 忽略回滚异常
                        }
                    }
                }
                
                foreach (var transaction in _transactions)
                {
                    transaction?.Dispose();
                }
                
                _clients.Clear();
                _transactions.Clear();
                _disposed = true;
            }
        }
    }
}
