using Microsoft.Extensions.DependencyInjection;
using H.FamilyBucket.Repository.IRepositories;

namespace H.FamilyBucket.Repository
{
    /// <summary>
    /// 仓储注册构建器
    /// </summary>
    public class RepositoryRegistrationBuilder
    {
        private readonly IServiceCollection _services;

        public RepositoryRegistrationBuilder(IServiceCollection services)
        {
            _services = services;
        }

        /// <summary>
        /// 注册仓储
        /// </summary>
        public RepositoryRegistrationBuilder AddRepository<TInterface, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TInterface : class
            where TImplementation : class, TInterface
        {
            _services.AddRepository<TInterface, TImplementation>(lifetime);
            return this;
        }

        /// <summary>
        /// 注册实体仓储
        /// </summary>
        public RepositoryRegistrationBuilder AddRepository<TEntity, TKey, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IRepository<TEntity, TKey>
        {
            _services.AddRepository<TEntity, TKey, TImplementation>(lifetime);
            return this;
        }

        /// <summary>
        /// 注册只读仓储
        /// </summary>
        public RepositoryRegistrationBuilder AddReadOnlyRepository<TEntity, TKey, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IReadOnlyRepository<TEntity, TKey>
        {
            _services.AddReadOnlyRepository<TEntity, TKey, TImplementation>(lifetime);
            return this;
        }

        /// <summary>
        /// 注册高级仓储
        /// </summary>
        public RepositoryRegistrationBuilder AddAdvancedRepository<TEntity, TKey, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TEntity : class
            where TImplementation : class, IAdvancedRepository<TEntity, TKey>
        {
            _services.AddAdvancedRepository<TEntity, TKey, TImplementation>(lifetime);
            return this;
        }

        /// <summary>
        /// 注册工作单元
        /// </summary>
        public RepositoryRegistrationBuilder AddUnitOfWork<TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TImplementation : class, IUnitOfWork
        {
            _services.AddUnitOfWork<TImplementation>(lifetime);
            return this;
        }

        /// <summary>
        /// 注册分布式工作单元
        /// </summary>
        public RepositoryRegistrationBuilder AddDistributedUnitOfWork<TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
            where TImplementation : class, IDistributedUnitOfWork
        {
            _services.AddDistributedUnitOfWork<TImplementation>(lifetime);
            return this;
        }
    }
}
