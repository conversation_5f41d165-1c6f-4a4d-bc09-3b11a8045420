using System.Linq.Expressions;
using System.Threading;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.Specifications;

namespace H.FamilyBucket.Repository.Repositories
{
    /// <summary>
    /// Base repository implementation that can be inherited by specific ORM implementations
    /// </summary>
    /// <typeparam name="TEntity">Entity type</typeparam>
    /// <typeparam name="TKey">Primary key type</typeparam>
    public abstract class BaseRepository<TEntity, TKey> : IAdvancedRepository<TEntity, TKey> where TEntity : class
    {
        protected BaseRepository()
        {
        }

        #region Abstract Methods - Must be implemented by specific ORM repositories

        protected abstract Task<TEntity?> GetByIdInternalAsync(TKey id, CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<TEntity>> GetAllInternalAsync(CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<TEntity>> FindInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken);
        protected abstract Task<int> CountInternalAsync(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract Task<bool> ExistsInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken);

        protected abstract Task<TEntity> AddInternalAsync(TEntity entity, CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<TEntity>> AddRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);
        protected abstract Task<bool> UpdateInternalAsync(TEntity entity, CancellationToken cancellationToken);
        protected abstract Task<bool> UpdateRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);
        protected abstract Task<bool> DeleteInternalAsync(TEntity entity, CancellationToken cancellationToken);
        protected abstract Task<bool> DeleteRangeInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);

        protected abstract Task<IEnumerable<TEntity>> QueryInternalAsync(string sql, object? parameters, CancellationToken cancellationToken);
        protected abstract Task<int> ExecuteInternalAsync(string sql, object? parameters, CancellationToken cancellationToken);

        protected abstract Task<IDbTransaction> BeginTransactionInternalAsync(CancellationToken cancellationToken);

        // 新增的抽象方法
        protected abstract Task<TEntity?> FirstOrDefaultInternalAsync(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract Task<TEntity?> SingleOrDefaultInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken);
        protected abstract Task<IPagedResult<TEntity>> GetPagedInternalAsync(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract Task<IPagedResult<TEntity>> GetPagedInternalAsync<TOrderBy>(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate, Expression<Func<TEntity, TOrderBy>>? orderBy, bool ascending, CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<TResult>> SelectInternalAsync<TResult>(Expression<Func<TEntity, TResult>> selector, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract Task<TResult> AggregateInternalAsync<TResult>(Expression<Func<IQueryable<TEntity>, TResult>> aggregateExpression, Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract Task<int> BulkInsertInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);
        protected abstract Task<int> BulkUpdateInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);
        protected abstract Task<int> BulkDeleteInternalAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken);
        protected abstract Task<int> BulkDeleteInternalAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<dynamic>> QueryDynamicInternalAsync(string sql, object? parameters, CancellationToken cancellationToken);
        protected abstract Task<IEnumerable<T>> QueryInternalAsync<T>(string sql, object? parameters, CancellationToken cancellationToken);
        protected abstract Task<T> ExecuteScalarInternalAsync<T>(string sql, object? parameters, CancellationToken cancellationToken);
        protected abstract IAsyncEnumerable<TEntity> FindAsyncEnumerableInternal(Expression<Func<TEntity, bool>>? predicate, CancellationToken cancellationToken);
        protected abstract IAsyncEnumerable<TEntity> FindAsyncEnumerableInternal<TOrderBy>(Expression<Func<TEntity, bool>>? predicate, Expression<Func<TEntity, TOrderBy>>? orderBy, bool ascending, CancellationToken cancellationToken);

        // 规约模式抽象方法
        protected abstract Task<IEnumerable<TEntity>> FindInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);
        protected abstract Task<TEntity?> FirstOrDefaultInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);
        protected abstract Task<TEntity?> SingleOrDefaultInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);
        protected abstract Task<int> CountInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);
        protected abstract Task<bool> ExistsInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);
        protected abstract Task<IPagedResult<TEntity>> GetPagedInternalAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken);

        #endregion

        #region IReadOnlyRepository Implementation

        public virtual async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
        {
            return await GetByIdInternalAsync(id, cancellationToken);
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            return await GetAllInternalAsync(cancellationToken);
        }

        public virtual async Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
        {
            return await FindInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return await CountInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
        {
            return await ExistsInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return await FirstOrDefaultInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<TEntity?> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
        {
            return await SingleOrDefaultInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<IPagedResult<TEntity>> GetPagedAsync(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return await GetPagedInternalAsync(pageIndex, pageSize, predicate, cancellationToken);
        }

        public virtual async Task<IPagedResult<TEntity>> GetPagedAsync<TOrderBy>(int pageIndex, int pageSize, Expression<Func<TEntity, bool>>? predicate = null, Expression<Func<TEntity, TOrderBy>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
        {
            return await GetPagedInternalAsync(pageIndex, pageSize, predicate, orderBy, ascending, cancellationToken);
        }

        public virtual async Task<IEnumerable<TResult>> SelectAsync<TResult>(Expression<Func<TEntity, TResult>> selector, Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return await SelectInternalAsync(selector, predicate, cancellationToken);
        }

        public virtual async Task<TResult> AggregateAsync<TResult>(Expression<Func<IQueryable<TEntity>, TResult>> aggregateExpression, Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return await AggregateInternalAsync(aggregateExpression, predicate, cancellationToken);
        }

        public virtual async Task<IEnumerable<TEntity>> QueryAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await QueryInternalAsync(sql, parameters, cancellationToken);
        }

        public virtual async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await QueryInternalAsync<T>(sql, parameters, cancellationToken);
        }

        public virtual async Task<IEnumerable<dynamic>> QueryDynamicAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await QueryDynamicInternalAsync(sql, parameters, cancellationToken);
        }

        public virtual async Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await ExecuteScalarInternalAsync<T>(sql, parameters, cancellationToken);
        }

        public virtual IAsyncEnumerable<TEntity> FindAsyncEnumerable(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
        {
            return FindAsyncEnumerableInternal(predicate, cancellationToken);
        }

        public virtual IAsyncEnumerable<TEntity> FindAsyncEnumerable<TOrderBy>(Expression<Func<TEntity, bool>>? predicate = null, Expression<Func<TEntity, TOrderBy>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
        {
            return FindAsyncEnumerableInternal(predicate, orderBy, ascending, cancellationToken);
        }

        #endregion

        #region IRepository Command Operations

        public virtual async Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default)
        {
            return await AddInternalAsync(entity, cancellationToken);
        }

        public virtual async Task<IEnumerable<TEntity>> AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await AddRangeInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<bool> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
        {
            return await UpdateInternalAsync(entity, cancellationToken);
        }

        public virtual async Task<bool> UpdateRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await UpdateRangeInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<bool> DeleteAsync(TKey id, CancellationToken cancellationToken = default)
        {
            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity == null)
                return false;
            return await DeleteAsync(entity, cancellationToken);
        }

        public virtual async Task<bool> DeleteAsync(TEntity entity, CancellationToken cancellationToken = default)
        {
            return await DeleteInternalAsync(entity, cancellationToken);
        }

        public virtual async Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await DeleteRangeInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<int> ExecuteAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            return await ExecuteInternalAsync(sql, parameters, cancellationToken);
        }

        public virtual async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            return await BeginTransactionInternalAsync(cancellationToken);
        }

        public virtual async Task CommitAsync(IDbTransaction transaction, CancellationToken cancellationToken = default)
        {
            await transaction.CommitAsync(cancellationToken);
        }

        public virtual async Task RollbackAsync(IDbTransaction transaction, CancellationToken cancellationToken = default)
        {
            await transaction.RollbackAsync(cancellationToken);
        }

        #endregion

        #region IAdvancedRepository Implementation

        public virtual async Task<IPagedResult<T>> GetPagedAsync<T>(string sql, int pageIndex, int pageSize, object? parameters = null, CancellationToken cancellationToken = default)
        {
            // 这个方法需要在具体实现中重写，因为它需要特定的SQL分页逻辑
            throw new NotImplementedException("This method must be implemented in the concrete repository class");
        }

        public virtual async Task<int> BulkInsertAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await BulkInsertInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<int> BulkUpdateAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await BulkUpdateInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<int> BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
        {
            return await BulkDeleteInternalAsync(predicate, cancellationToken);
        }

        public virtual async Task<int> BulkDeleteAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            return await BulkDeleteInternalAsync(entities, cancellationToken);
        }

        public virtual async Task<System.Data.DataTable> QueryDataTableAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default)
        {
            // 这个方法需要在具体实现中重写
            throw new NotImplementedException("This method must be implemented in the concrete repository class");
        }

        #endregion

        #region Specification Pattern Implementation

        public virtual async Task<IEnumerable<TEntity>> FindAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await FindInternalAsync(specification, cancellationToken);
        }

        public virtual async Task<TEntity?> FirstOrDefaultAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await FirstOrDefaultInternalAsync(specification, cancellationToken);
        }

        public virtual async Task<TEntity?> SingleOrDefaultAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await SingleOrDefaultInternalAsync(specification, cancellationToken);
        }

        public virtual async Task<int> CountAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await CountInternalAsync(specification, cancellationToken);
        }

        public virtual async Task<bool> ExistsAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await ExistsInternalAsync(specification, cancellationToken);
        }

        public virtual async Task<IPagedResult<TEntity>> GetPagedAsync(ISpecification<TEntity> specification, CancellationToken cancellationToken = default)
        {
            return await GetPagedInternalAsync(specification, cancellationToken);
        }

        #endregion
    }
}