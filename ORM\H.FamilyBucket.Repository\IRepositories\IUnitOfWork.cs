using System;
using System.Threading;
using System.Threading.Tasks;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 工作单元接口
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// 获取仓储
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <typeparam name="TKey">主键类型</typeparam>
        /// <returns>仓储实例</returns>
        IRepository<TEntity, TKey> GetRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 获取只读仓储
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <typeparam name="TKey">主键类型</typeparam>
        /// <returns>只读仓储实例</returns>
        IReadOnlyRepository<TEntity, TKey> GetReadOnlyRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 获取高级仓储
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <typeparam name="TKey">主键类型</typeparam>
        /// <returns>高级仓储实例</returns>
        IAdvancedRepository<TEntity, TKey> GetAdvancedRepository<TEntity, TKey>() where TEntity : class;

        /// <summary>
        /// 开始事务
        /// </summary>
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 提交事务
        /// </summary>
        Task CommitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 回滚事务
        /// </summary>
        Task RollbackAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 保存更改
        /// </summary>
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行事务
        /// </summary>
        Task<T> ExecuteTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行事务（无返回值）
        /// </summary>
        Task ExecuteTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// 是否在事务中
        /// </summary>
        bool IsInTransaction { get; }

        /// <summary>
        /// 当前事务
        /// </summary>
        IDbTransaction? CurrentTransaction { get; }
    }
}
