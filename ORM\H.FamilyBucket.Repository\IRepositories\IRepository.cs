﻿namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// Base repository interface that defines common operations
    /// </summary>
    /// <typeparam name="TEntity">Entity type</typeparam>
    /// <typeparam name="TK<PERSON>">Primary key type</typeparam>
    public interface IRepository<TEntity, TKey> : IReadOnlyRepository<TEntity, TKey> where TEntity : class
    {
        #region Command Operations

        /// <summary>
        /// Add new entity
        /// </summary>
        Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);

        /// <summary>
        /// Add multiple entities
        /// </summary>
        Task<IEnumerable<TEntity>> AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Update existing entity
        /// </summary>
        Task<bool> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);

        /// <summary>
        /// Update multiple entities
        /// </summary>
        Task<bool> UpdateRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete entity by id
        /// </summary>
        Task<bool> DeleteAsync(TKey id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete entity
        /// </summary>
        Task<bool> DeleteAsync(TEntity entity, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete multiple entities
        /// </summary>
        Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

        #endregion

        #region SQL Operations

        /// <summary>
        /// Execute raw SQL command
        /// </summary>
        Task<int> ExecuteAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        #endregion

        #region Transaction Operations

        /// <summary>
        /// Begin transaction
        /// </summary>
        Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Commit transaction
        /// </summary>
        Task CommitAsync(IDbTransaction transaction, CancellationToken cancellationToken = default);

        /// <summary>
        /// Rollback transaction
        /// </summary>
        Task RollbackAsync(IDbTransaction transaction, CancellationToken cancellationToken = default);

        #endregion
    }

    /// <summary>
    /// Database transaction interface
    /// </summary>
    public interface IDbTransaction : IDisposable
    {
        /// <summary>
        /// Commit the transaction
        /// </summary>
        Task CommitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rollback the transaction
        /// </summary>
        Task RollbackAsync(CancellationToken cancellationToken = default);
    }
}
