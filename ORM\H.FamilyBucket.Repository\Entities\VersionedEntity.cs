using System.ComponentModel.DataAnnotations;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 版本控制实体基类
    /// </summary>
    /// <typeparam name="TKey">主键类型</typeparam>
    public abstract class VersionedEntity<TKey> : SoftDeleteEntity<TKey>, IVersionedEntity
    {
        /// <summary>
        /// 版本号
        /// </summary>
        [Timestamp]
        public virtual long Version { get; set; }
    }

    /// <summary>
    /// 版本控制实体基类（long主键）
    /// </summary>
    public abstract class VersionedEntity : VersionedEntity<long>
    {
    }
}
