using System;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar����ʵ��
    /// </summary>
    public class SqlSugarDbTransaction : IDbTransaction
    {
        private readonly SqlSugarClient _db;
        private bool _disposed;

        public SqlSugarDbTransaction(SqlSugarClient db)
        {
            _db = db;
        }

        public async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            await _db.Ado.CommitTranAsync();
        }

        public async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            await _db.Ado.RollbackTranAsync();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _db.Ado.RollbackTran();
                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
