using System;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.IRepositories;
using H.FamilyBucket.Repository.SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar.SqlServer
{
    /// <summary>
    /// SQL Server特定的工作单元实现
    /// </summary>
    public class SqlServerUnitOfWork : SqlSugarUnitOfWork
    {
        private readonly SqlServerDatabaseConfig _sqlServerConfig;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContextFactory">数据库上下文工厂</param>
        /// <param name="config">SQL Server配置</param>
        public SqlServerUnitOfWork(ISqlSugarDbContextFactory dbContextFactory, SqlServerDatabaseConfig config) 
            : base(dbContextFactory, config.Key)
        {
            _sqlServerConfig = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 构造函数（使用数据库键）
        /// </summary>
        /// <param name="dbContextFactory">数据库上下文工厂</param>
        /// <param name="databaseKey">数据库键</param>
        public SqlServerUnitOfWork(ISqlSugarDbContextFactory dbContextFactory, string? databaseKey = null) 
            : base(dbContextFactory, databaseKey)
        {
            _sqlServerConfig = new SqlServerDatabaseConfig("default", "");
        }

        /// <summary>
        /// 创建SQL Server仓储实例
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <typeparam name="TKey">主键类型</typeparam>
        /// <returns>SQL Server仓储实例</returns>
        public SqlServerRepository<TEntity, TKey> GetSqlServerRepository<TEntity, TKey>() where TEntity : class, new()
        {
            return new SqlServerRepository<TEntity, TKey>(_dbContextFactory, _sqlServerConfig);
        }

        /// <summary>
        /// 创建仓储实例（重写以返回SQL Server特定实现）
        /// </summary>
        protected override IRepository<TEntity, TKey> CreateRepository<TEntity, TKey>() where TEntity : class
        {
            return (IRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(SqlServerRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _sqlServerConfig)!;
        }

        /// <summary>
        /// 创建只读仓储实例（重写以返回SQL Server特定实现）
        /// </summary>
        protected override IReadOnlyRepository<TEntity, TKey> CreateReadOnlyRepository<TEntity, TKey>() where TEntity : class
        {
            return (IReadOnlyRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(SqlServerRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _sqlServerConfig)!;
        }

        /// <summary>
        /// 创建高级仓储实例（重写以返回SQL Server特定实现）
        /// </summary>
        protected override IAdvancedRepository<TEntity, TKey> CreateAdvancedRepository<TEntity, TKey>() where TEntity : class
        {
            return (IAdvancedRepository<TEntity, TKey>)Activator.CreateInstance(
                typeof(SqlServerRepository<,>).MakeGenericType(typeof(TEntity), typeof(TKey)),
                _dbContextFactory, _sqlServerConfig)!;
        }

        /// <summary>
        /// 执行SQL Server特定的事务操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作</param>
        /// <param name="isolationLevel">事务隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public async Task<T> ExecuteInTransactionAsync<T>(
            Func<Task<T>> operation, 
            System.Data.IsolationLevel isolationLevel = System.Data.IsolationLevel.ReadCommitted,
            CancellationToken cancellationToken = default)
        {
            using var transaction = await BeginTransactionAsync(cancellationToken);
            try
            {
                var result = await operation();
                await transaction.CommitAsync(cancellationToken);
                return result;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }

        /// <summary>
        /// 执行SQL Server特定的事务操作（无返回值）
        /// </summary>
        /// <param name="operation">操作</param>
        /// <param name="isolationLevel">事务隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        public async Task ExecuteInTransactionAsync(
            Func<Task> operation, 
            System.Data.IsolationLevel isolationLevel = System.Data.IsolationLevel.ReadCommitted,
            CancellationToken cancellationToken = default)
        {
            using var transaction = await BeginTransactionAsync(cancellationToken);
            try
            {
                await operation();
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接是否正常</returns>
        public async Task<bool> CheckConnectionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var client = GetClient();
                await client.Ado.GetScalarAsync("SELECT 1");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据库信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数据库信息</returns>
        public async Task<dynamic> GetDatabaseInfoAsync(CancellationToken cancellationToken = default)
        {
            var client = GetClient();
            var sql = @"
                SELECT 
                    DB_NAME() AS DatabaseName,
                    @@VERSION AS ServerVersion,
                    @@SERVERNAME AS ServerName,
                    GETDATE() AS CurrentDateTime,
                    USER_NAME() AS CurrentUser";
            
            return await client.Ado.SqlQuerySingleAsync<dynamic>(sql);
        }
    }
}
