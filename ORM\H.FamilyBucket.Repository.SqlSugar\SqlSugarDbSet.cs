using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using H.FamilyBucket.Repository.Context;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar数据库集合实现
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    public class SqlSugarDbSet<TEntity> : IDbSet<TEntity> where TEntity : class, new()
    {
        private readonly SqlSugarClient _client;

        public SqlSugarDbSet(SqlSugarClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        /// <summary>
        /// 添加实体
        /// </summary>
        public async Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default)
        {
            await _client.Insertable(entity).ExecuteCommandAsync();
            return entity;
        }

        /// <summary>
        /// 添加多个实体
        /// </summary>
        public async Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
        {
            await _client.Insertable(entities.ToList()).ExecuteCommandAsync();
        }

        /// <summary>
        /// 更新实体
        /// </summary>
        public void Update(TEntity entity)
        {
            _client.Updateable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 更新多个实体
        /// </summary>
        public void UpdateRange(IEnumerable<TEntity> entities)
        {
            _client.Updateable(entities.ToList()).ExecuteCommand();
        }

        /// <summary>
        /// 删除实体
        /// </summary>
        public void Remove(TEntity entity)
        {
            _client.Deleteable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        public void RemoveRange(IEnumerable<TEntity> entities)
        {
            _client.Deleteable(entities.ToList()).ExecuteCommand();
        }

        /// <summary>
        /// 根据ID查找实体
        /// </summary>
        public async Task<TEntity?> FindAsync(object id, CancellationToken cancellationToken = default)
        {
            return await _client.Queryable<TEntity>().InSingleAsync(id);
        }

        /// <summary>
        /// 根据多个ID查找实体
        /// </summary>
        public async Task<TEntity?> FindAsync(object[] ids, CancellationToken cancellationToken = default)
        {
            if (ids == null || ids.Length == 0)
                return null;

            // 对于复合主键的情况，这里需要根据具体的主键字段来构建查询
            // 这是一个简化的实现，实际使用时可能需要更复杂的逻辑
            return await _client.Queryable<TEntity>().InSingleAsync(ids[0]);
        }

        /// <summary>
        /// 附加实体
        /// </summary>
        public void Attach(TEntity entity)
        {
            // SqlSugar不需要显式附加实体
            // 这里可以添加一些跟踪逻辑如果需要的话
        }

        /// <summary>
        /// 分离实体
        /// </summary>
        public void Detach(TEntity entity)
        {
            // SqlSugar不需要显式分离实体
            // 这里可以添加一些清理逻辑如果需要的话
        }
    }
}
