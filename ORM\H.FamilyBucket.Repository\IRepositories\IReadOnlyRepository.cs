using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace H.FamilyBucket.Repository.IRepositories
{
    /// <summary>
    /// 只读仓储接口
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TKey">主键类型</typeparam>
    public interface IReadOnlyRepository<TEntity, TKey> where TEntity : class
    {
        #region 基础查询

        /// <summary>
        /// 根据主键获取实体
        /// </summary>
        Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取所有实体
        /// </summary>
        Task<IEnumerable<TEntity>> GetAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件查询实体
        /// </summary>
        Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取实体数量
        /// </summary>
        Task<int> CountAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查是否存在
        /// </summary>
        Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取第一个实体
        /// </summary>
        Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取单个实体
        /// </summary>
        Task<TEntity?> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);

        #endregion

        #region 分页查询

        /// <summary>
        /// 分页查询
        /// </summary>
        Task<IPagedResult<TEntity>> GetPagedAsync(
            int pageIndex, 
            int pageSize, 
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 分页查询（带排序）
        /// </summary>
        Task<IPagedResult<TEntity>> GetPagedAsync<TOrderBy>(
            int pageIndex, 
            int pageSize, 
            Expression<Func<TEntity, bool>>? predicate = null,
            Expression<Func<TEntity, TOrderBy>>? orderBy = null,
            bool ascending = true,
            CancellationToken cancellationToken = default);

        #endregion

        #region 投影查询

        /// <summary>
        /// 查询并投影
        /// </summary>
        Task<IEnumerable<TResult>> SelectAsync<TResult>(
            Expression<Func<TEntity, TResult>> selector,
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 聚合查询
        /// </summary>
        Task<TResult> AggregateAsync<TResult>(
            Expression<Func<IQueryable<TEntity>, TResult>> aggregateExpression,
            Expression<Func<TEntity, bool>>? predicate = null,
            CancellationToken cancellationToken = default);

        #endregion

        #region SQL查询

        /// <summary>
        /// 执行SQL查询
        /// </summary>
        Task<IEnumerable<TEntity>> QueryAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询指定类型
        /// </summary>
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询动态对象
        /// </summary>
        Task<IEnumerable<dynamic>> QueryDynamicAsync(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 查询单个值
        /// </summary>
        Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

        #endregion

        #region 异步枚举

        /// <summary>
        /// 异步枚举查询
        /// </summary>
        IAsyncEnumerable<TEntity> FindAsyncEnumerable(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步枚举查询（带排序）
        /// </summary>
        IAsyncEnumerable<TEntity> FindAsyncEnumerable<TOrderBy>(
            Expression<Func<TEntity, bool>>? predicate = null,
            Expression<Func<TEntity, TOrderBy>>? orderBy = null,
            bool ascending = true,
            CancellationToken cancellationToken = default);

        #endregion
    }
}
