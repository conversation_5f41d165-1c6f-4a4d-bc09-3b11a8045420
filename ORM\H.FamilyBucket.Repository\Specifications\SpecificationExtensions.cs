namespace H.FamilyBucket.Repository.Specifications
{
    /// <summary>
    /// 规约扩展方法
    /// </summary>
    public static class SpecificationExtensions
    {
        /// <summary>
        /// And操作
        /// </summary>
        public static ISpecification<T> And<T>(this ISpecification<T> left, ISpecification<T> right)
        {
            return new AndSpecification<T>(left, right);
        }

        /// <summary>
        /// Or操作
        /// </summary>
        public static ISpecification<T> Or<T>(this ISpecification<T> left, ISpecification<T> right)
        {
            return new OrSpecification<T>(left, right);
        }

        /// <summary>
        /// Not操作
        /// </summary>
        public static ISpecification<T> Not<T>(this ISpecification<T> specification)
        {
            return new NotSpecification<T>(specification);
        }
    }
}
