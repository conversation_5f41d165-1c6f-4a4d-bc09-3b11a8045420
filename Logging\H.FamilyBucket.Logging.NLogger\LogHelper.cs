﻿using System;

namespace H.FamilyBucket.Logging.NLogger
{
    /// <summary>
    /// 静态日志工具类，支持直接使用
    /// </summary>
    public static class LogHelper
    {
        private static readonly Lazy<ILoggerService> _logger = new Lazy<ILoggerService>(() => 
            new NLoggerService("LogHelper"));

        /// <summary>
        /// 获取日志服务实例
        /// </summary>
        public static ILoggerService Logger => _logger.Value;

        /// <summary>
        /// 使用指定配置初始化日志
        /// </summary>
        /// <param name="configuration">日志配置</param>
        public static void Initialize(LoggerConfiguration configuration)
        {
            // 重新创建日志实例以应用新配置
            var newLogger = new NLoggerService("LogHelper", configuration);
            typeof(LogHelper).GetField("_logger", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)?
                .SetValue(null, new Lazy<ILoggerService>(() => newLogger));
        }

        /// <summary>
        /// 使用指定配置文件路径初始化日志
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public static void Initialize(string configFilePath)
        {
            var configuration = new LoggerConfiguration
            {
                ConfigFilePath = configFilePath
            };
            Initialize(configuration);
        }

        /// <summary>
        /// 记录跟踪日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Trace(string message, params object[] args)
        {
            Logger.Trace(message, args);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Debug(string message, params object[] args)
        {
            Logger.Debug(message, args);
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Info(string message, params object[] args)
        {
            Logger.Info(message, args);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Warn(string message, params object[] args)
        {
            Logger.Warn(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Error(string message, params object[] args)
        {
            Logger.Error(message, args);
        }

        /// <summary>
        /// 记录错误日志（包含异常信息）
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Error(Exception exception, string message, params object[] args)
        {
            Logger.Error(exception, message, args);
        }

        /// <summary>
        /// 记录致命错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Fatal(string message, params object[] args)
        {
            Logger.Fatal(message, args);
        }

        /// <summary>
        /// 记录致命错误日志（包含异常信息）
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void Fatal(Exception exception, string message, params object[] args)
        {
            Logger.Fatal(exception, message, args);
        }

        /// <summary>
        /// 检查是否启用了指定级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>是否启用</returns>
        public static bool IsEnabled(LogLevel level)
        {
            return Logger.IsEnabled(level);
        }

        /// <summary>
        /// 创建指定类别的日志服务
        /// </summary>
        /// <param name="categoryName">类别名称</param>
        /// <param name="configuration">日志配置</param>
        /// <returns>日志服务实例</returns>
        public static ILoggerService CreateLogger(string categoryName, LoggerConfiguration? configuration = null)
        {
            return new NLoggerService(categoryName, configuration);
        }

        /// <summary>
        /// 创建指定类型的日志服务
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="configuration">日志配置</param>
        /// <returns>日志服务实例</returns>
        public static ILoggerService CreateLogger<T>(LoggerConfiguration? configuration = null)
        {
            return new NLoggerService(typeof(T).FullName, configuration);
        }
    }
}
