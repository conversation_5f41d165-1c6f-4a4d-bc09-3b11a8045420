using System;
using System.Collections.Generic;
using H.FamilyBucket.Repository.SqlSugar;
using SqlSugar;

namespace H.FamilyBucket.Repository.SqlSugar.SqlServer
{
    /// <summary>
    /// SQL Server数据库配置
    /// </summary>
    public class SqlServerDatabaseConfig : SqlSugarDatabaseConfig
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">数据库标识</param>
        /// <param name="connectionString">连接字符串</param>
        public SqlServerDatabaseConfig(string key, string connectionString) 
            : base(key, connectionString, DbType.SqlServer)
        {
            // SQL Server 特定的默认配置
            CommandTimeout = 60; // SQL Server 默认超时时间稍长
            EnableLogging = true;
            EnableSensitiveDataLogging = false;
            MaxRetryCount = 3;
            RetryDelay = TimeSpan.FromSeconds(2);
            PoolSize = 100;
            EnableConnectionPooling = true;
        }

        /// <summary>
        /// 是否启用SQL Server特定的功能
        /// </summary>
        public bool EnableSqlServerFeatures { get; set; } = true;

        /// <summary>
        /// 是否启用多活动结果集(MARS)
        /// </summary>
        public bool EnableMultipleActiveResultSets { get; set; } = true;

        /// <summary>
        /// 连接超时时间（秒）
        /// </summary>
        public int ConnectionTimeout { get; set; } = 30;

        /// <summary>
        /// 是否启用加密连接
        /// </summary>
        public bool EnableEncryption { get; set; } = false;

        /// <summary>
        /// 是否信任服务器证书
        /// </summary>
        public bool TrustServerCertificate { get; set; } = true;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string? ApplicationName { get; set; }

        /// <summary>
        /// 工作站ID
        /// </summary>
        public string? WorkstationId { get; set; }

        /// <summary>
        /// 是否启用统计信息
        /// </summary>
        public bool EnableStatistics { get; set; } = false;

        /// <summary>
        /// 数据包大小
        /// </summary>
        public int PacketSize { get; set; } = 8000;

        /// <summary>
        /// 构建完整的连接字符串
        /// </summary>
        /// <returns>完整的连接字符串</returns>
        public string BuildConnectionString()
        {
            var builder = new Microsoft.Data.SqlClient.SqlConnectionStringBuilder(ConnectionString);

            if (EnableMultipleActiveResultSets)
                builder.MultipleActiveResultSets = true;

            if (ConnectionTimeout > 0)
                builder.ConnectTimeout = ConnectionTimeout;

            if (EnableEncryption)
                builder.Encrypt = Microsoft.Data.SqlClient.SqlConnectionEncryptOption.Mandatory;

            if (TrustServerCertificate)
                builder.TrustServerCertificate = true;

            if (!string.IsNullOrEmpty(ApplicationName))
                builder.ApplicationName = ApplicationName;

            if (!string.IsNullOrEmpty(WorkstationId))
                builder.WorkstationID = WorkstationId;

            if (PacketSize > 0 && PacketSize != 8000)
                builder.PacketSize = PacketSize;

            return builder.ConnectionString;
        }

        /// <summary>
        /// 创建SQL Server配置的快速方法
        /// </summary>
        /// <param name="key">数据库标识</param>
        /// <param name="server">服务器地址</param>
        /// <param name="database">数据库名</param>
        /// <param name="userId">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="integratedSecurity">是否使用集成安全验证</param>
        /// <returns>SQL Server数据库配置</returns>
        public static SqlServerDatabaseConfig Create(
            string key, 
            string server, 
            string database, 
            string? userId = null, 
            string? password = null, 
            bool integratedSecurity = false)
        {
            var builder = new Microsoft.Data.SqlClient.SqlConnectionStringBuilder
            {
                DataSource = server,
                InitialCatalog = database,
                IntegratedSecurity = integratedSecurity,
                TrustServerCertificate = true,
                MultipleActiveResultSets = true
            };

            if (!integratedSecurity && !string.IsNullOrEmpty(userId))
            {
                builder.UserID = userId;
                builder.Password = password ?? string.Empty;
            }

            return new SqlServerDatabaseConfig(key, builder.ConnectionString);
        }
    }
}
