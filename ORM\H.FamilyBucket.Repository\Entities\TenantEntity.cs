using System.ComponentModel.DataAnnotations;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 租户实体基类
    /// </summary>
    /// <typeparam name="TKey">主键类型</typeparam>
    public abstract class TenantEntity<TKey> : VersionedEntity<TKey>, ITenantEntity
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        [Required]
        [MaxLength(64)]
        public virtual string TenantId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 租户实体基类（long主键）
    /// </summary>
    public abstract class TenantEntity : TenantEntity<long>
    {
    }
}
