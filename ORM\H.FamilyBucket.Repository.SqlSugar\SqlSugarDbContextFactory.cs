using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace H.FamilyBucket.Repository.SqlSugar
{
    /// <summary>
    /// SqlSugar数据库上下文工厂实现
    /// </summary>
    public class SqlSugarDbContextFactory : ISqlSugarDbContextFactory, IDisposable
    {
        private readonly MultiDatabaseManager _databaseManager;
        private bool _disposed = false;

        public SqlSugarDbContextFactory(MultiDatabaseManager databaseManager)
        {
            _databaseManager = databaseManager ?? throw new ArgumentNullException(nameof(databaseManager));
        }

        /// <summary>
        /// 获取默认数据库客户端
        /// </summary>
        public SqlSugarClient GetClient()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.GetClient();
        }

        /// <summary>
        /// 获取指定数据库客户端
        /// </summary>
        public SqlSugarClient GetClient(string key)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.GetClient(key);
        }

        /// <summary>
        /// 获取所有数据库客户端
        /// </summary>
        public IEnumerable<SqlSugarClient> GetAllClients()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.GetAllClients();
        }

        /// <summary>
        /// 获取指定数据库的客户端列表
        /// </summary>
        public IEnumerable<SqlSugarClient> GetClients(params string[] keys)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.GetClients(keys);
        }

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        public bool DatabaseExists(string key)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.DatabaseExists(key);
        }

        /// <summary>
        /// 获取所有数据库键
        /// </summary>
        public IEnumerable<string> GetDatabaseKeys()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            return _databaseManager.GetDatabaseKeys();
        }

        /// <summary>
        /// 创建分布式事务管理器
        /// </summary>
        public DistributedTransactionManager CreateDistributedTransaction()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            var transactionManager = new DistributedTransactionManager();
            transactionManager.AddClients(GetAllClients());
            return transactionManager;
        }

        /// <summary>
        /// 创建分布式事务管理器（指定数据库）
        /// </summary>
        public DistributedTransactionManager CreateDistributedTransaction(params string[] databaseKeys)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SqlSugarDbContextFactory));
                
            if (databaseKeys == null || !databaseKeys.Any())
                throw new ArgumentException("Database keys cannot be null or empty");
                
            var transactionManager = new DistributedTransactionManager();
            transactionManager.AddClients(GetClients(databaseKeys));
            return transactionManager;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                // 注意：这里不释放 _databaseManager，因为它可能被其他地方使用
                // _databaseManager 应该由 DI 容器管理生命周期
                _disposed = true;
            }
        }
    }
}
