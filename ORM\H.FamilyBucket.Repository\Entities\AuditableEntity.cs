using System;
using System.ComponentModel.DataAnnotations;

namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// 审计实体基类
    /// </summary>
    /// <typeparam name="TK<PERSON>">主键类型</typeparam>
    public abstract class AuditableEntity<TKey> : BaseEntity<TKey>, IAuditableEntity
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public virtual DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(256)]
        public virtual string? CreatedBy { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public virtual DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(256)]
        public virtual string? UpdatedBy { get; set; }
    }

    /// <summary>
    /// 审计实体基类（long主键）
    /// </summary>
    public abstract class AuditableEntity : AuditableEntity<long>
    {
    }
}
