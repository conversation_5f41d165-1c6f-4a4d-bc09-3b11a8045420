namespace H.FamilyBucket.Repository.Entities
{
    /// <summary>
    /// Base entity interface with a generic Id property
    /// </summary>
    /// <typeparam name="T<PERSON><PERSON>">Type of the Id property</typeparam>
    public interface IEntity<TKey>
    {
        /// <summary>
        /// Unique identifier for the entity
        /// </summary>
        TKey Id { get; set; }
    }

    /// <summary>
    /// Base entity interface with long Id
    /// </summary>
    public interface IEntity : IEntity<long>
    {
    }
}