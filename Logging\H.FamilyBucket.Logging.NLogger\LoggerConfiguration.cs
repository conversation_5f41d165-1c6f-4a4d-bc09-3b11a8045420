﻿using System;

namespace H.FamilyBucket.Logging.NLogger
{
    /// <summary>
    /// 日志配置类
    /// </summary>
    public class LoggerConfiguration
    {
        /// <summary>
        /// NLog配置文件路径，默认为 "nlog.config"
        /// </summary>
        public string ConfigFilePath { get; set; } = "nlog.config";

        /// <summary>
        /// 是否自动重载配置文件，默认为 true
        /// </summary>
        public bool AutoReload { get; set; } = true;

        /// <summary>
        /// 内部日志级别，默认为 Info
        /// </summary>
        public string InternalLogLevel { get; set; } = "Info";

        /// <summary>
        /// 内部日志文件路径，默认为 "logs/internal-nlog.txt"
        /// </summary>
        public string InternalLogFile { get; set; } = "logs/internal-nlog.txt";

        /// <summary>
        /// 日志目录，默认为 "logs"
        /// </summary>
        public string LogDirectory { get; set; } = "logs";

        /// <summary>
        /// 是否启用控制台输出，默认为 true
        /// </summary>
        public bool EnableConsole { get; set; } = true;

        /// <summary>
        /// 控制台输出的最小日志级别，默认为 Info
        /// </summary>
        public LogLevel ConsoleMinLevel { get; set; } = LogLevel.Info;

        /// <summary>
        /// 是否启用文件输出，默认为 true
        /// </summary>
        public bool EnableFile { get; set; } = true;

        /// <summary>
        /// 文件归档大小限制（字节），默认为 10MB
        /// </summary>
        public long ArchiveAboveSize { get; set; } = 10485760; // 10MB

        /// <summary>
        /// 最大归档文件数量，默认为 20
        /// </summary>
        public int MaxArchiveFiles { get; set; } = 20;

        /// <summary>
        /// 是否启用归档文件压缩，默认为 false
        /// </summary>
        public bool EnableArchiveFileCompression { get; set; } = false;
    }
}
